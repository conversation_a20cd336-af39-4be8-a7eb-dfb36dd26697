<template>
    <MisPopupFrame :title="'视频详情'"
                :hasArrow="true"
                :showTitle="showTitle"
                :data="data"
                @close="handleClose"
    >
        <component :is="currentCom" :data="data" :baseInfo="baseInfo" />
    </MisPopupFrame>
</template>

<script setup>
import {computed, ref, watch} from "vue";
import MisPopupFrame from "../MisPopupFrame.vue";
import VideoInfo from "./VideoInfo.vue";
import {popupApiInfo} from "@/components/GisMap/popup/popupApi.js";
import {mapStates} from "@/components/GisMap/mapStates.js";
import {ElMessage} from "element-plus";

const props = defineProps({
    showTitle: true,
    data: {
        type: Object,
        default: () => ({}),
    },
    closeEvent: {
        type: Function,
        required: false
    }
});

const currentCom = computed(() => {
    return VideoInfo;
});

const baseInfo = ref([]);

// 获取基础信息
const getBaseInfo = async () => {
    try {
    const response = await popupApiInfo[props.data?.layerId]({
        deviceId: props.data?.deviceId,
        channelId: props.data?.channelId,
    });
        baseInfo.value={};
        if (response?.code === 200 && response?.data) {
            baseInfo.value = {
                id: props.data?.id,
                name: props.data?.name,
                deviceId: props.data?.deviceId,
                channelId: props.data?.channelId,
                hlsUrl: response.data?.hlsUrl,
                online: response.data?.online !== false ? (props.data?.online || true) : false
            };
        } else {
            ElMessage.error('获取视频流失败');
        }
    } catch (error) {
        console.error('获取视频流失败:', error);
        ElMessage.error('获取视频流失败');
    }
};

const handleClose = () => {
    props.closeEvent?.();
    //清除高亮
    mapStates.earth.entity.clearHighlight();
};
watch(
    () => props.data,
    () => {
        getBaseInfo();
    },
    {
        deep: true,
        immediate: true,
    }
);
</script>

<style lang="scss" scoped>

</style>
