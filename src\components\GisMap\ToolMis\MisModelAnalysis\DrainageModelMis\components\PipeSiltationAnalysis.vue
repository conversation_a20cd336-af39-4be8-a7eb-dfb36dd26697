<template>
    <div class="mis-pipe-siltation-analysis">
        <div class="content-area">
            <!-- 淤积管网 -->
            <div class="hazard-section">
                <div class="section-header">
                    <div class="section-title">淤积管网</div>
                </div>
                <div class="table-container">
                    <el-table
                            :data="siltationData"
                            class="data-table dark-table"
                            :border="false"
                            size="small"
                    >
                        <el-table-column prop="xh" label="序号" width="80"/>
                        <el-table-column prop="gxbm" label="管线编码"/>
                        <el-table-column label="操作" width="100">
                            <template #default="scope">
                                <div class="action-buttons">
                                    <span class="action-btn">定位</span>
                                </div>
                            </template>
                        </el-table-column>
                    </el-table>
                </div>
            </div>
        </div>
    </div>
</template>

<script setup>
import {onMounted, reactive, ref} from 'vue'

const props = defineProps({
    data: {
        type: Object,
        default: () => ({}),
    }
})

const state = reactive({});

// 淤积数据
const siltationData = ref([
    {
        xh: '1',
        gxbm: 'DX123333',
    },
    {
        xh: '2',
        gxbm: 'DX123334',
    },
    {
        xh: '3',
        gxbm: 'DX123335',
    },
    {
        xh: '4',
        gxbm: 'DX123336',
    }
]);

onMounted(() => {});
</script>

<style lang="scss" scoped>
.mis-pipe-siltation-analysis {
  position: fixed;
  top: 18vh;
  right: 2vw;
  width: 360px;
  display: flex;
  flex-direction: column;
  border-radius: 8px;
  pointer-events: all;

  &::before {
    content: "";
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    backdrop-filter: blur(10px);
    background: rgba(255, 255, 255, 1);
    border-radius: 8px;
    border: 1px solid rgba(255, 255, 255, 0.4);
    z-index: -1;
  }

  .content-area {
    flex: 1;
    padding: 10px 20px;
    overflow: auto;

    .hazard-section, .protection-section {
      margin-bottom: 20px;

      .section-header {
        display: flex;
        align-items: center;
        justify-content: space-between;
        margin-bottom: 10px;
        border-bottom: 1px solid rgba(255, 255, 255, 0.8);
        padding-bottom: 10px;

        .section-title {
          font-size: 14px;
          font-weight: bold;
          color: rgba(50, 50, 50, 0.8);
        }
      }

      .table-container {
        .data-table {
          width: 100%;
          color: rgba(50, 50, 50, 0.8);
          font-size: 12px;

          // 覆盖 el-table 的默认样式
          :deep(.el-table) {
            background-color: transparent;
            color: rgba(50, 50, 50, 0.8);

            .el-table__header-wrapper {
              .el-table__header {
                background-color: transparent;

                th {
                  background-color: transparent !important;
                  color: rgba(50, 50, 50, 0.8) !important;
                  border-bottom: 1px solid rgba(50, 50, 50, 0.2) !important;
                }
              }
            }

            .el-table__body-wrapper {
              .el-table__body {
                background-color: transparent;

                tr {
                  background-color: transparent !important;

                  td {
                    background-color: transparent !important;
                    color: rgba(50, 50, 50, 0.8) !important;
                    border-bottom: 1px solid rgba(50, 50, 50, 0.2) !important;
                  }

                  &:last-child {
                    td {
                      border-bottom: none !important;
                    }
                  }
                }
              }
            }
          }

          .action-buttons {
            display: flex;
            gap: 12px;

            .action-btn {
              color: #409eff;
              cursor: pointer;
              font-size: 12px;
              transition: all 0.2s ease;

              &:hover {
                color: #66b1ff;
              }
            }
          }
        }

        // 深色表格专用样式 - 独立的作用域
        .dark-table {
          :deep(.el-table) {
            background-color: transparent !important;

            // 表格边框
            &::before {
              display: none !important;
            }

            .el-table__border-line {
              display: none !important;
            }

            // 表头样式 - 使用指定的背景色
            .el-table__header-wrapper {
              .el-table__header {
                background-color: rgba(50, 50, 50, 0) !important;

                th {
                  background-color: rgba(50, 50, 50, 0.2) !important;
                  color: rgba(50, 50, 50, 0.8) !important;
                  border-bottom: 1px solid rgba(50, 50, 50, 0.2) !important;
                  border-right: 1px solid rgba(50, 50, 50, 0.2) !important;
                  //font-weight: 500;
                  padding: 8px 12px;

                  &.is-leaf {
                    border-bottom: 1px solid rgba(50, 50, 50, 0.8) !important;
                  }

                  &:last-child {
                    border-right: none !important;
                  }

                  .cell {
                    color: rgba(50, 50, 50, 0.2) !important;
                  }
                }
              }
            }

            // 表格主体样式
            .el-table__body-wrapper {
              .el-table__body {
                background-color: transparent !important;

                tr {
                  background-color: rgba(50, 50, 50, 0.2) !important;

                  &:hover {
                    background-color: rgba(50, 50, 50, 0.2) !important;
                  }

                  td {
                    background-color: rgba(50, 50, 50, 0) !important;
                    color: rgba(50, 50, 50, 0.8) !important;
                    border-bottom: 1px solid rgba(50, 50, 50, 0.2) !important;
                    border-right: 1px solid rgba(50, 50, 50, 0.2) !important;
                    padding: 8px 12px;

                    .cell {
                      color: rgba(50, 50, 50, 0.8) !important;
                    }

                    &:last-child {
                      border-right: none !important;
                    }
                  }

                  &:last-child {
                    td {
                      border-bottom: none !important;
                    }
                  }
                }
              }
            }

            // 空数据样式
            .el-table__empty-block {
              background-color: transparent !important;

              .el-table__empty-text {
                color: rgba(50, 50, 50, 0.8) !important;
              }
            }
          }
        }

        // 额外的强制样式，确保优先级
        .dark-table.el-table {
          background-color: transparent !important;

        }

        .dark-table .el-table__header-wrapper .el-table__header {
          background-color: rgba(50, 50, 50, 0) !important;
        }

        .dark-table .el-table__header-wrapper .el-table__header th {
          background-color: rgba(50, 50, 50, 0) !important;
          color: rgba(50, 50, 50, 0.8) !important;
          border-bottom: 1px solid rgba(50, 50, 50, 0.2) !important;
          border-right: 1px solid rgba(50, 50, 50, 0.2) !important;
        }

        .dark-table .el-table__body-wrapper .el-table__body tr {
          background-color: rgba(50, 50, 50, 0) !important;
        }

        .dark-table .el-table__body-wrapper .el-table__body tr td {
          background-color: rgba(50, 50, 50, 0) !important;
          color: rgba(50, 50, 50, 0.8) !important;
          border-bottom: 1px solid rgba(50, 50, 50, 0.2) !important;
          border-right: 1px solid rgba(50, 50, 50, 0.2) !important;
        }
      }
    }
  }

  // 全局样式覆盖，确保深色表格样式生效
  :deep(.dark-table .el-table) {
    background-color: transparent !important;
  }

  :deep(.dark-table .el-table__header-wrapper .el-table__header) {
    background-color: rgba(50, 50, 50, 0) !important;
  }

  :deep(.dark-table .el-table__header-wrapper .el-table__header th) {
    background-color: rgba(50, 50, 50, 0) !important;
    color: rgba(50, 50, 50, 0.8) !important;
    border: 1px solid rgba(50, 50, 50, 0.2) !important;
  }

  :deep(.dark-table .el-table__body-wrapper .el-table__body tr) {
    background-color: rgba(50, 50, 50, 0) !important;
  }

  :deep(.dark-table .el-table__body-wrapper .el-table__body tr td) {
    background-color: rgba(50, 50, 50, 0) !important;
    color: rgba(50, 50, 50, 0.8) !important;
    border: 1px solid rgba(50, 50, 50, 0.2) !important;
  }

  :deep(.el-table) {
    --el-table-border-color: rgba(11, 30, 65, 0) !important;
  }
}
</style>