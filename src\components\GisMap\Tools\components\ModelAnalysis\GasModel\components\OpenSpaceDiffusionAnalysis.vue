<template>
    <div class="open-space-diffusion-analysis">
        <div class="analysis-header">
            <div class="title">扩散分析</div>
            <div class="close-btn" @click="$emit('close')">
                <el-icon>
                    <Close/>
                </el-icon>
            </div>
        </div>

        <div class="content-area">
            <!-- 报警信息 -->
            <div class="alarm-section">
                <div class="section-header">
                    <div class="section-title">报警信息</div>

                    <!-- 设备信息和监测曲线标签页 -->
                    <div class="tab-container">
                        <div
                                class="tab-item"
                                :class="{ active: activeTab === 'device' }"
                                @click="activeTab = 'device'"
                        >
                            设备信息
                        </div>
                        <div
                                class="tab-item"
                                :class="{ active: activeTab === 'curve' }"
                                @click="activeTab = 'curve'"
                        >
                            监测曲线
                        </div>
                    </div>
                </div>

                <!-- 设备信息内容 -->
                <div v-show="activeTab === 'device'" class="device-info">
                    <div class="info-row">
                        <span class="label">设备名称：</span>
                        <span class="value">{{ state.deviceInfo?.deviceName || "" }}</span>
                    </div>
                    <div class="info-row">
                        <span class="label">设备编码：</span>
                        <span class="value">{{ state.deviceInfo?.indexCode || "" }}</span>
                    </div>
                    <div class="info-row">
                        <span class="label">报警类型：</span>
                        <span class="value">{{ state.alarmInfo?.alarmTypeName || "" }}</span>
                    </div>
                    <div class="info-row">
                        <span class="label">报警等级：</span>
                        <span class="value alarm-level"
                              :style="{ color: state.alarmInfo?.alarmLevel ? alarmLevelColorMap[state.alarmInfo?.alarmLevel]: '#ffffff'}"
                        >{{ state.alarmInfo?.alarmLevelName || "" }}</span>
                    </div>
                    <div class="info-row">
                        <span class="label">报警值：</span>
                        <span class="value alarm-value"
                          :style="{ color: state.alarmInfo?.alarmLevel ? alarmLevelColorMap[state.alarmInfo?.alarmLevel]: '#ffffff'}"
                        >
                          {{
                            state.alarmInfo?.monitorIndexName.includes("状态") && state.alarmInfo?.alarmValue === "1" ? "异常" : state.alarmInfo?.alarmValue || ""
                            }}
                          {{ state.alarmInfo?.alarmValueUnit ? "（" + state.alarmInfo?.alarmValueUnit + "）" : "" }}
                        </span>
                    </div>
                    <div class="info-row">
                        <span class="label">监测对象：</span>
                        <span class="value">
                            {{ state.deviceInfo?.monitorObjectName || "" }}
                            {{ state.deviceInfo?.monitorTargetName ? `(${state.deviceInfo?.monitorTargetName})` : "" }}
                        </span>
                    </div>
                    <div class="info-row">
                        <span class="label">设备位置：</span>
                        <span class="value">{{ state.deviceInfo?.address || "" }}</span>
                    </div>
                </div>

                <!-- 监测曲线内容 -->
                <div v-show="activeTab === 'curve'" class="curve-info">
                    <MonitorCurve :data="data"/>
                </div>
            </div>

            <!-- 影响范围 -->
            <div class="impact-range-section">
                <div class="section-header">
                    <div class="section-title">影响范围</div>
                </div>
                <div class="impact-list">
                    <div class="impact-item">
                        <div class="impact-icon red"></div>
                        <span class="impact-text">16%浓度面积(m²)：67.92</span>
                    </div>
                    <div class="impact-item">
                        <div class="impact-icon orange"></div>
                        <span class="impact-text">9.5%浓度面积(m²)：116.30</span>
                    </div>
                    <div class="impact-item">
                        <div class="impact-icon yellow"></div>
                        <span class="impact-text">5%浓度面积(m²)：225.53</span>
                    </div>
                </div>
            </div>

            <!-- 危险源 -->
            <div class="hazard-section">
                <div class="section-header">
                    <div class="section-title">危险源</div>
                    <div class="filter-buttons">
                        <div
                                class="filter-btn"
                                :class="{ active: activeHazardFilter === 'diffusion' }"
                                @click="activeHazardFilter = 'diffusion'"
                        >
                            扩散范围内
                        </div>
                        <div
                                class="filter-btn"
                                :class="{ active: activeHazardFilter === 'surrounding' }"
                                @click="activeHazardFilter = 'surrounding'"
                        >
                            周边1公里内
                        </div>
                    </div>
                </div>
                <div class="table-container">
                    <el-table
                            :data="hazardData"
                            class="data-table dark-table"
                            :border="false"
                            size="small"
                    >
                        <el-table-column prop="name" label="危险源名称" width="120"/>
                        <el-table-column prop="buildingType" label="建筑物类型" width="100"/>
                        <el-table-column prop="contact" label="联系人" width="80"/>
                        <el-table-column prop="phone" label="联系电话" width="100"/>
                        <el-table-column label="操作" width="120">
                            <template #default="scope">
                                <div class="action-buttons">
                                    <span class="action-btn">定位</span>
                                </div>
                            </template>
                        </el-table-column>
                    </el-table>
                </div>
            </div>

            <!-- 防护目标 -->
            <div class="protection-section">
                <div class="section-header">
                    <div class="section-title">防护目标</div>
                    <div class="filter-buttons">
                        <div
                                class="filter-btn"
                                :class="{ active: activeProtectionFilter === 'diffusion' }"
                                @click="activeProtectionFilter = 'diffusion'"
                        >
                            扩散范围内
                        </div>
                        <div
                                class="filter-btn"
                                :class="{ active: activeProtectionFilter === 'surrounding' }"
                                @click="activeProtectionFilter = 'surrounding'"
                        >
                            周边1公里内
                        </div>
                    </div>
                </div>
                <div class="table-container">
                    <el-table
                            :data="protectionData"
                            class="data-table dark-table"
                            :border="false"
                            size="small"
                    >
                        <el-table-column prop="name" label="防护目标名称" width="120"/>
                        <el-table-column prop="buildingType" label="建筑物类型" width="100"/>
                        <el-table-column prop="contact" label="联系人" width="80"/>
                        <el-table-column prop="phone" label="联系电话" width="100"/>
                        <el-table-column label="操作" width="120">
                            <template #default="scope">
                                <div class="action-buttons">
                                    <span class="action-btn">定位</span>
                                </div>
                            </template>
                        </el-table-column>
                    </el-table>
                </div>
            </div>
        </div>
    </div>
</template>

<script setup>
import {onMounted, reactive, ref, watch} from 'vue'
import {Close} from '@element-plus/icons-vue'
import {popupApiInfo, popupMonitorAlarmApiInfo} from "@/components/GisMap/popup/popupApi.js";
import {alarmLevelColorMap} from "@/components/GisMap/popup/device.js";
import MonitorCurve from "./MonitorCurve.vue";

const props = defineProps({
    data: {
        type: Object,
        default: () => ({}),
    }
})

const state = reactive({
    deviceInfo: null, //设备信息
    alarmInfo: null, //报警信息
});

// 危险源数据
const hazardData = ref([
    {
        name: '走官营村',
        buildingType: '',
        contact: '',
        phone: ''
    },
    {
        name: '',
        buildingType: '',
        contact: '',
        phone: ''
    },
    {
        name: '',
        buildingType: '',
        contact: '',
        phone: ''
    }
]);

// 防护目标数据
const protectionData = ref([
    {
        name: '',
        buildingType: '',
        contact: '',
        phone: ''
    },
    {
        name: '',
        buildingType: '',
        contact: '',
        phone: ''
    }
]);

const emit = defineEmits(['close']);
const activeTab = ref('device');
const activeHazardFilter = ref('diffusion');
const activeProtectionFilter = ref('diffusion');

const getDeviceInfo = () => {
    state.deviceInfo = null;
    if (popupApiInfo[props.data?.layerId]) {
        popupApiInfo[props.data?.layerId](props.data?.id).then(res => {
            state.deviceInfo = res?.data;
        });
    }
}

const getDeviceAlarmInfo = () => {
    state.alarmInfo = null;
    if (popupMonitorAlarmApiInfo[props.data?.layerId]) {
        popupMonitorAlarmApiInfo[props.data?.layerId](props.data?.alarmId).then(res => {
            state.alarmInfo = res?.data;
        })
    }
}

onMounted(() => {
    getDeviceInfo();
    getDeviceAlarmInfo();
});
</script>

<style lang="scss" scoped>
.open-space-diffusion-analysis {
  position: absolute;
  top: 0px;
  right: 3vw;
  width: 400px;
  display: flex;
  flex-direction: column;
  border-radius: 8px;
  pointer-events: all;

  &::before {
    content: "";
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    backdrop-filter: blur(10px);
    background: rgba(11, 30, 65, 0.8);
    border-radius: 8px;
    border: 1px solid #19385c;
    z-index: -1;
  }

  .analysis-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    border-top-left-radius: 12px;
    padding: 10px 20px;
    font-size: 16px;
    font-weight: bold;
    color: #ffffff;
    border-bottom: 1px solid #19385c;

    .title {
      font-size: 16px;
      font-weight: bold;
      color: #ffffff;
    }

    .close-btn {
      font-size: 20px;
      cursor: pointer;
      color: #ffffff;

      &:hover {
        opacity: 0.8;
      }
    }
  }

  .content-area {
    flex: 1;
    padding: 10px 20px;
    overflow: auto;

    .alarm-section {
      .section-header {
        display: flex;
        align-items: center;
        justify-content: space-between;
        margin-bottom: 10px;
        border-bottom: 1px solid #19385c;
        padding-bottom: 10px;

        .section-title {
          font-size: 14px;
          font-weight: bold;
          color: #ffffff;
        }

        .tab-container {
          display: flex;
          align-items: center;

          .tab-item {
            color: rgba(255, 255, 255, 0.8);
            font-size: 13px;
            letter-spacing: 1px;
            position: relative;
            margin-left: 20px;
            cursor: pointer;

            &::after {
              content: "";
              position: absolute;
              left: 50%;
              transform: translateX(-50%);
              bottom: -10px;
              width: 0;
              height: 2px;
              background: #fff;
              opacity: 0;
              border-radius: 1px;
              transition: all 0.2s ease;
            }

            &.active {
              position: relative;
              color: #fff;

              &:after {
                opacity: 1;
                width: 40px;
              }
            }
          }
        }
      }

      .device-info {
        .info-row {
          display: flex;
          margin-bottom: 8px;

          .label {
            color: rgba(255, 255, 255, 0.8);
            font-size: 12px;
            text-align: right;
            min-width: 70px;
          }

          .value {
            color: #ffffff;
            font-size: 12px;

            &.alarm-level, &.alarm-value {
              color: #ff4757;
              font-weight: bold;
            }
          }
        }
      }

      .curve-info {
        .curve-placeholder {
          height: 120px;
          display: flex;
          align-items: center;
          justify-content: center;
          color: rgba(255, 255, 255, 0.6);
          font-size: 12px;
          border: 1px dashed rgba(255, 255, 255, 0.3);
          border-radius: 4px;
        }
      }
    }

    .impact-range-section {
      margin-bottom: 20px;

      .section-header {
        display: flex;
        align-items: center;
        justify-content: space-between;
        margin-bottom: 10px;
        border-bottom: 1px solid #19385c;
        padding-bottom: 10px;

        .section-title {
          font-size: 14px;
          font-weight: bold;
          color: #ffffff;
        }
      }

      .impact-list {
        display: flex;
        flex-direction: column;
        gap: 8px;

        .impact-item {
          display: flex;
          align-items: center;

          .impact-icon {
            width: 16px;
            height: 16px;
            border-radius: 50%;
            margin-right: 8px;

            &.red {
              background-color: #ff4757;
            }

            &.orange {
              background-color: #ffa502;
            }

            &.yellow {
              background-color: #ffd32a;
            }
          }

          .impact-text {
            color: #ffffff;
            font-size: 12px;
          }
        }
      }
    }

    .hazard-section, .protection-section {
      margin-bottom: 10px;

      .section-header {
        display: flex;
        align-items: center;
        justify-content: space-between;
        margin-bottom: 10px;
        border-bottom: 1px solid #19385c;
        padding-bottom: 10px;

        .section-title {
          font-size: 14px;
          font-weight: bold;
          color: #ffffff;
        }

        .filter-buttons {
          display: flex;
          align-items: center;
          gap: 20px;

          .filter-btn {
            color: rgba(255, 255, 255, 0.8);
            font-size: 13px;
            letter-spacing: 1px;
            position: relative;
            cursor: pointer;

            &:hover {
              color: rgba(255, 255, 255, 0.9);
            }

            &.active {
              position: relative;
              color: #fff;

              &:after {
                opacity: 1;
                width: 40px;
              }
            }

            &:after {
              content: "";
              position: absolute;
              left: 50%;
              transform: translateX(-50%);
              bottom: -10px;
              width: 0;
              height: 2px;
              background: #fff;
              opacity: 0;
              border-radius: 1px;
              transition: all 0.2s ease;
            }
          }
        }
      }

      .table-container {
        .data-table {
          width: 100%;
          color: #ffffff;
          font-size: 12px;

          // 覆盖 el-table 的默认样式
          :deep(.el-table) {
            background-color: transparent;
            color: #ffffff;

            .el-table__header-wrapper {
              .el-table__header {
                background-color: transparent;

                th {
                  background-color: transparent !important;
                  color: rgba(255, 255, 255, 0.8) !important;
                  border-bottom: 1px solid #19385c !important;
                }
              }
            }

            .el-table__body-wrapper {
              .el-table__body {
                background-color: transparent;

                tr {
                  background-color: transparent !important;

                  td {
                    background-color: transparent !important;
                    color: #ffffff !important;
                    border-bottom: 1px solid #19385c !important;
                  }

                  &:last-child {
                    td {
                      border-bottom: none !important;
                    }
                  }
                }
              }
            }
          }

          .action-buttons {
            display: flex;
            gap: 12px;

            .action-btn {
              color: #409eff;
              cursor: pointer;
              font-size: 12px;
              transition: all 0.2s ease;

              &:hover {
                color: #66b1ff;
              }
            }
          }
        }

        // 深色表格专用样式 - 独立的作用域
        .dark-table {
          :deep(.el-table) {
            background-color: transparent !important;

            // 表格边框
            &::before {
              display: none !important;
            }

            .el-table__border-line {
              display: none !important;
            }

            // 表头样式 - 使用指定的背景色
            .el-table__header-wrapper {
              .el-table__header {
                background-color: rgba(11, 30, 65, 0.8) !important;

                th {
                  background-color: rgba(11, 30, 65, 0.8) !important;
                  color: rgba(255, 255, 255, 0.95) !important;
                  border-bottom: 1px solid #19385c !important;
                  border-right: 1px solid #19385c !important;
                  //font-weight: 500;
                  padding: 8px 12px;

                  &.is-leaf {
                    border-bottom: 1px solid #19385c !important;
                  }

                  &:last-child {
                    border-right: none !important;
                  }

                  .cell {
                    color: rgba(255, 255, 255, 0.95) !important;
                  }
                }
              }
            }

            // 表格主体样式
            .el-table__body-wrapper {
              .el-table__body {
                background-color: transparent !important;

                tr {
                  background-color: rgba(11, 30, 65, 0.8) !important;

                  &:hover {
                    background-color: rgba(64, 158, 255, 0.1) !important;
                  }

                  td {
                    background-color: rgba(11, 30, 65, 0.8) !important;
                    color: #ffffff !important;
                    border-bottom: 1px solid #19385c !important;
                    border-right: 1px solid #19385c !important;
                    padding: 8px 12px;

                    .cell {
                      color: #ffffff !important;
                    }

                    &:last-child {
                      border-right: none !important;
                    }
                  }

                  &:last-child {
                    td {
                      border-bottom: none !important;
                    }
                  }
                }
              }
            }

            // 空数据样式
            .el-table__empty-block {
              background-color: transparent !important;

              .el-table__empty-text {
                color: rgba(255, 255, 255, 0.6) !important;
              }
            }
          }
        }

        // 额外的强制样式，确保优先级
        .dark-table.el-table {
          background-color: transparent !important;

        }

        .dark-table .el-table__header-wrapper .el-table__header {
          background-color: rgba(11, 30, 65, 0.2) !important;
        }

        .dark-table .el-table__header-wrapper .el-table__header th {
          background-color: rgba(11, 30, 65, 0.2) !important;
          color: rgba(255, 255, 255, 0.95) !important;
          border-bottom: 1px solid #19385c !important;
          border-right: 1px solid #19385c !important;
        }

        .dark-table .el-table__body-wrapper .el-table__body tr {
          background-color: rgba(11, 30, 65, 0.2) !important;
        }

        .dark-table .el-table__body-wrapper .el-table__body tr td {
          background-color: rgba(11, 30, 65, 0.2) !important;
          color: #ffffff !important;
          border-bottom: 1px solid #19385c !important;
          border-right: 1px solid #19385c !important;
        }
      }
    }
  }

// 全局样式覆盖，确保深色表格样式生效
:deep(.dark-table .el-table) {
   background-color: transparent !important;
 }

 :deep(.dark-table .el-table__header-wrapper .el-table__header) {
   background-color: rgba(11, 30, 65, 0.8) !important;
 }

 :deep(.dark-table .el-table__header-wrapper .el-table__header th) {
   background-color: rgba(11, 30, 65, 0.8) !important;
   color: rgba(255, 255, 255, 0.95) !important;
   border-bottom: 1px solid #19385c !important;
   border-right: 1px solid #19385c !important;
 }

 :deep(.dark-table .el-table__body-wrapper .el-table__body tr) {
   background-color: rgba(11, 30, 65, 0.2) !important;
 }

 :deep(.dark-table .el-table__body-wrapper .el-table__body tr td) {
   background-color: rgba(11, 30, 65, 0.2) !important;
   color: #ffffff !important;
   border-bottom: 1px solid #19385c !important;
   border-right: 1px solid #19385c !important;
}

 :deep(.el-table) {
    --el-table-border-color: rgba(11, 30, 65, 0) !important;
 }

}

</style>