<template>
  <div class="component-model-list">
      <div class="model_level">
          <div
                  v-for="item in modelData"
                  :key="item.title"
                  class="butt-normal"
                  :class="[state.selectedLevel === item.id ? 'butt-active' : '']"
                  @click="handleClickLevel(item.id)"
          >
              {{ item.title }}
          </div>
      </div>
      <StructuralBottleneckAnalysis v-if="state.selectedLevel === 1" />
      <PipeSiltationAnalysis v-if="state.selectedLevel === 2" />
      <PipeOverflowModel
          v-if="state.selectedLevel === 3"
          @analyze="handleAnalyze"
      />
      <PumpStationModel
          v-if="state.selectedLevel === 4"
          @analyze="handleAnalyze"
      />
      <WaterloggingModel
          v-if="state.selectedLevel === 5"
          @analyze="handleAnalyze"
      />
      <StructuralLegend v-if="state.selectedLevel === 1" />
      <SiltationLegend v-if="state.selectedLevel === 2" />
  </div>
</template>

<script setup>
import bus from "@/utils/mitt.js";
import {reactive} from "vue";
import StructuralBottleneckAnalysis from "./components/StructuralBottleneckAnalysis.vue";
import PipeSiltationAnalysis from "./components/PipeSiltationAnalysis.vue";
import PipeOverflowModel from "./components/PipeOverflowModel.vue";
import PumpStationModel from "./components/PumpStationModel.vue";
import WaterloggingModel from "./components/WaterloggingModel.vue";
import StructuralLegend from "./components/StructuralLegend.vue";
import SiltationLegend from "./components/SiltationLegend.vue";

const state = reactive({
    selectedLevel: 1,
});
const modelData = reactive([
    {
        id: 1,
        title: "结构瓶颈模拟分析",
    },
    {
        id: 2,
        title: "管网淤积预测预警分析",
    },
    {
        id: 3,
        title: "管网溢流预测预警分析",
    },
    {
        id: 4,
        title: "泵站负荷能力分析",
    },
    {
        id: 5,
        title: "内涝预测预警分析",
    },
]);

const handleClickLevel = (e) => {
    //todo 激活模型
    state.selectedLevel = e;
};

const handleAnalyze = (formData) => {
    //todo 模型分析 formData
};

</script>

<style lang="scss" scoped>
.component-model-list {
  width: 178px;
  pointer-events: all;

  .model_level {
    width: 178px;
  }

  .butt-normal {
    margin-bottom: 10px;
    padding: 8px 10px;
    border-radius: 4px;
    cursor: pointer;
    background: rgba(13, 37, 82, 0.8);
    border: 1px solid #182d54;
    font-size: 14px;
    font-weight: bold;
    color: #ffffff;
    text-align: center;

    &:hover {
      background: rgba(26, 142, 231, 0.8);
      border: 1px solid rgba(26, 142, 231, 1);
    }
  }

  .butt-active {
    background: rgba(26, 142, 231, 0.8);
    border: 1px solid rgba(26, 142, 231, 1);
    color: #ffffff;
  }

}
</style>
