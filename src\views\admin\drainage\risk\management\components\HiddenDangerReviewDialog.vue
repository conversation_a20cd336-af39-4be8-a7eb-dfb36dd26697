<template>
  <el-dialog
    v-model="dialogVisible"
    title="隐患复查"
    width="600px"
    :close-on-click-modal="false"
    :before-close="handleClose"
    class="review-dialog"
  >
    <el-form
      ref="formRef"
      :model="formData"
      :rules="formRules"
      label-width="140px"
      class="review-form"
    >
      <!-- 完成时间 -->
      <el-form-item label="完成时间：" prop="dealTime">
        <el-date-picker
          v-model="formData.dealTime"
          type="datetime"
          placeholder="请选择完成时间"
          format="YYYY-MM-DD HH:mm:ss"
          value-format="YYYY-MM-DD HH:mm:ss"
          style="width: 100%"
        />
      </el-form-item>

      <!-- 是否按期完成 -->
      <el-form-item label="是否按期完成：" prop="onSchedule">
        <el-select
          v-model="formData.onSchedule"
          placeholder="请选择"
          style="width: 100%"
        >
          <el-option label="是" :value="true" />
          <el-option label="否" :value="false" />
        </el-select>
      </el-form-item>

      <!-- 完成情况 -->
      <el-form-item label="完成情况：" prop="description">
        <el-input
          v-model="formData.description"
          type="textarea"
          :rows="4"
          placeholder="请输入完成情况描述"
          maxlength="500"
          show-word-limit
        />
      </el-form-item>

      <!-- 复查人 -->
      <el-form-item label="复查人：" prop="handleUserName">
        <el-input
          v-model="formData.handleUserName"
          placeholder="请输入复查人姓名"
          maxlength="50"
        />
      </el-form-item>

      <!-- 复查结果 -->
      <el-form-item label="复查结果：" prop="isPass">
        <el-select
          v-model="formData.isPass"
          placeholder="请选择复查结果"
          style="width: 100%"
        >
          <el-option label="通过" :value="0" />
          <el-option label="驳回" :value="1" />
        </el-select>
      </el-form-item>

      <!-- 备注 -->
      <el-form-item label="备注：" prop="remark">
        <el-input
          v-model="formData.remark"
          type="textarea"
          :rows="3"
          placeholder="请输入备注信息"
          maxlength="200"
          show-word-limit
        />
      </el-form-item>
    </el-form>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">取 消</el-button>
        <el-button type="danger" @click="handleReject">驳 回</el-button>
        <el-button type="primary" @click="handlePass">通 过</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
import { ref, computed, watch, nextTick } from 'vue';
import { ElMessage } from 'element-plus';
import { reviewHiddenDanger } from '@/api/drainage';

const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  },
  dangerId: {
    type: String,
    default: ''
  }
});

const emit = defineEmits(['update:visible', 'success']);

// 表单引用
const formRef = ref();

// 对话框可见性
const dialogVisible = computed({
  get: () => props.visible,
  set: (val) => emit('update:visible', val)
});

// 表单数据
const formData = ref({
  dangerId: '',
  dealTime: '',
  description: '',
  handleUserName: '',
  isPass: 0, // 0：通过，1：驳回
  onSchedule: true,
  remark: ''
});

// 表单验证规则
const formRules = {
  dealTime: [
    { required: true, message: '请选择完成时间', trigger: 'change' }
  ],
  onSchedule: [
    { required: true, message: '请选择是否按期完成', trigger: 'change' }
  ],
  description: [
    { required: true, message: '请输入完成情况描述', trigger: 'blur' },
    { min: 1, max: 500, message: '完成情况描述长度在 1 到 500 个字符', trigger: 'blur' }
  ],
  handleUserName: [
    { required: true, message: '请输入复查人姓名', trigger: 'blur' },
    { min: 1, max: 50, message: '复查人姓名长度在 1 到 50 个字符', trigger: 'blur' }
  ],
  isPass: [
    { required: true, message: '请选择复查结果', trigger: 'change' }
  ]
};

// 监听弹窗显示，初始化表单数据
watch(() => props.visible, (newVal) => {
  if (newVal) {
    resetForm();
    formData.value.dangerId = props.dangerId;
  }
});

// 重置表单
const resetForm = () => {
  formData.value = {
    dangerId: '',
    dealTime: '',
    description: '',
    handleUserName: '',
    isPass: 0,
    onSchedule: true,
    remark: ''
  };
  nextTick(() => {
    formRef.value?.clearValidate();
  });
};

// 处理通过
const handlePass = async () => {
  try {
    await formRef.value.validate();
    formData.value.isPass = 0; // 设置为通过
    await submitReview();
  } catch (error) {
    console.log('表单验证失败:', error);
  }
};

// 处理驳回
const handleReject = async () => {
  try {
    await formRef.value.validate();
    formData.value.isPass = 1; // 设置为驳回
    await submitReview();
  } catch (error) {
    console.log('表单验证失败:', error);
  }
};

// 提交复查结果
const submitReview = async () => {
  try {
    const res = await reviewHiddenDanger(formData.value);
    if (res && res.code === 200) {
      ElMessage.success('复查提交成功');
      emit('success');
      handleClose();
    } else {
      ElMessage.error(res?.msg || '复查提交失败');
    }
  } catch (error) {
    console.error('复查提交失败:', error);
    ElMessage.error('复查提交失败');
  }
};

// 关闭弹窗
const handleClose = () => {
  dialogVisible.value = false;
  resetForm();
};
</script>

<style scoped>
.review-dialog {
  .review-form {
    padding: 20px 0;
  }

  .dialog-footer {
    display: flex;
    justify-content: flex-end;
    gap: 12px;
  }
}

/* 表单项样式优化 */
:deep(.el-form-item__label) {
  font-weight: 500;
  color: #303133;
}

:deep(.el-form-item) {
  margin-bottom: 24px;
}

/* 文本域样式 */
:deep(.el-textarea__inner) {
  resize: none;
}

/* 选择器样式 */
:deep(.el-select) {
  width: 100%;
}

/* 日期选择器样式 */
:deep(.el-date-editor) {
  width: 100%;
}

/* 按钮样式 */
:deep(.el-button) {
  min-width: 80px;
  height: 36px;
}
</style>