/**
 * 动态路由生成器
 * 根据用户权限动态生成可访问的路由配置
 */

import { adminRoutes } from './admin.js'
import { filterRoutesByPermission } from '@/utils/permission'

/**
 * 根据用户权限生成动态路由
 * @param {Array} userPermissions - 用户权限列表
 * @returns {Array} - 过滤后的路由配置
 */
export function generateDynamicRoutes(userPermissions) {
  if (!userPermissions || !Array.isArray(userPermissions) || userPermissions.length === 0) {
    console.warn('用户权限为空，返回空路由配置')
    return []
  }
  
  console.log('开始根据用户权限生成动态路由:', userPermissions)
  
  // 过滤admin路由
  const filteredAdminRoutes = filterRoutesByPermission(adminRoutes, userPermissions)
  
  console.log('过滤后的路由配置:', filteredAdminRoutes)
  
  return filteredAdminRoutes
}

/**
 * 获取用户可访问的菜单列表
 * @param {Array} userPermissions - 用户权限列表
 * @returns {Array} - 可访问的菜单列表
 */
export function getAccessibleMenus(userPermissions) {
  const dynamicRoutes = generateDynamicRoutes(userPermissions)
  
  // 从路由配置中提取菜单信息
  function extractMenus(routes, menus = []) {
    routes.forEach(route => {
      if (route.meta && route.meta.title && route.name) {
        menus.push({
          name: route.name,
          title: route.meta.title,
          icon: route.meta.icon,
          path: route.path
        })
      }
      
      if (route.children && route.children.length > 0) {
        extractMenus(route.children, menus)
      }
    })
    
    return menus
  }
  
  return extractMenus(dynamicRoutes)
}

/**
 * 检查路由是否在用户权限范围内
 * @param {string} routeName - 路由名称
 * @param {Array} userPermissions - 用户权限列表
 * @returns {boolean} - 是否有权限
 */
export function isRouteAccessible(routeName, userPermissions) {
  if (!routeName || !userPermissions || !Array.isArray(userPermissions)) {
    return false
  }
  
  return userPermissions.includes(routeName)
}

/**
 * 为Vue Router添加动态路由
 * @param {Object} router - Vue Router实例
 * @param {Array} userPermissions - 用户权限列表
 */
export function addDynamicRoutesToRouter(router, userPermissions) {
  const dynamicRoutes = generateDynamicRoutes(userPermissions)
  
  // 移除之前添加的动态路由（如果有的话）
  // 注意：这里需要根据实际情况处理路由的添加和移除
  
  // 添加新的动态路由
  dynamicRoutes.forEach(route => {
    router.addRoute(route)
  })
  
  console.log('动态路由已添加到路由器')
}