<!-- 桥梁基本信息 -->
<template>
    <div class="bridge-base-info">
        <div class="bridge-content">
            <!-- 左侧信息区域 -->
            <div class="info-section">
                <!-- 基本信息 -->
                <div class="info-item">
                    <span class="info-label">桥梁名称：</span>
                    <span class="info-value">{{ baseInfo?.bridgeName || '--' }}</span>
                </div>
                <div class="info-item">
                    <span class="info-label">所在道路：</span>
                    <span class="info-value">{{ baseInfo?.roadName || '--' }}</span>
                </div>
                <div class="info-item">
                    <span class="info-label">所在位置：</span>
                    <span class="info-value">
                        {{ baseInfo?.city || '--' }}{{ baseInfo?.county || '--' }}{{ baseInfo?.roadName || '--' }}
                    </span>
                </div>
                <div class="info-item">
                    <span class="info-label">评估时间：</span>
                    <span class="info-value">{{ baseInfo?.scoreTime || '--' }}</span>
                </div>
                <!-- 安全评估 -->
                <div class="safety-assessment">
                    <span class="assessment-label">安全评估得分：</span>
                    <span class="assessment-score">{{ baseInfo?.score || '--' }}分</span>
                    <!--                    <span class="assessment-time">（评估时间：{{ baseInfo?.scoreTime || '&#45;&#45;' }}）</span>-->
                </div>

            </div>
            <!-- 右侧图片区域 -->
            <div class="image-section">
                <div v-if="baseInfo?.imageUrl" class="bridge-image">
                    <img :src="baseInfo.imageUrl" alt="桥梁图片"/>
                </div>
                <div v-else class="image-placeholder">
                    <div class="placeholder-content">
                        <i class="el-icon-picture placeholder-icon"></i>
                        <div class="placeholder-text">暂无图片</div>
                    </div>
                </div>
            </div>
        </div>
        <!-- 简介 -->
        <div class="description-section">
            <div class="description-label">简介：</div>
            <div class="description-content">
                {{ baseInfo?.description || '--' }}
            </div>
        </div>
    </div>
  <!-- 详细信息按钮 -->
    <div class="detail-button-section">
        <button class="detail-button" @click="showDetailInfo">
            桥梁详细信息
        </button>
    </div>
</template>

<script setup>
import {watch} from "vue";
import bus from "@/utils/mitt.js";

const props = defineProps({
    baseInfo: {
        type: Object,
        default: () => ({}),
    },
    data: {
        type: Object,
        default: () => ({}),
    }
});

const showDetailInfo = () => {
    bus.emit('showBridgeDetailPosition', props.data);
    window.enterBridgeDetailMode(props.baseInfo?.id, props.baseInfo?.bridgeName);
};

// 监听数据变化
watch(() => props.baseInfo, () => {
    // console.log('baseInfo---bridge', props.baseInfo);
}, {deep: true});
</script>

<style lang="scss" scoped>
.bridge-base-info {
  height: 360px;
  overflow-y: auto;

  .bridge-content {
    display: flex;
    gap: 16px;
  }

  .info-section {
    flex: 1;
    padding: 16px;
    //background: #1a2332;
    color: #ffffff;
    border-radius: 4px;
    display: flex;
    flex-direction: column;
    gap: 12px;
  }

  .info-item {
    display: flex;
    align-items: center;
    font-size: 14px;

    .info-label {
      color: #8fa4b8;
      min-width: 80px;
    }

    .info-value {
      color: #ffffff;
      flex: 1;
    }
  }

  .safety-assessment {
    display: flex;
    align-items: center;
    font-size: 14px;
    //margin: 8px 0;

    .assessment-label {
      color: #8fa4b8;
    }

    .assessment-score {
      color: #00d4ff;
      font-weight: bold;
      margin: 0 8px;
    }

    .assessment-time {
      color: #8fa4b8;
      font-size: 12px;
    }
  }

  .description-section {
    flex: 1;
    margin: 0px 16px;

    .description-label {
      color: #8fa4b8;
      font-size: 14px;
      margin-bottom: 8px;
    }

    .description-content {
      color: #ffffff;
      font-size: 12px;
      line-height: 1.6;
      text-align: justify;
    }
  }

  .image-section {
    width: 240px;
    height: 180px;
    flex-shrink: 0;

    .bridge-image {
      width: 100%;
      height: 100%;
      border-radius: 4px;
      overflow: hidden;

      img {
        width: 100%;
        height: 100%;
        object-fit: cover;
      }
    }

    .image-placeholder {
      width: 100%;
      height: 100%;
      background: #f7f7f7;
      border-radius: 4px;
      display: flex;
      align-items: center;
      justify-content: center;

      .placeholder-content {
        display: flex;
        flex-direction: column;
        align-items: center;

        .placeholder-icon {
          font-size: 32px;
          color: #ccc;
          margin-bottom: 8px;
        }

        .placeholder-text {
          color: #999;
          font-size: 12px;
        }
      }
    }
  }
}

.detail-button-section {
    display: flex;
    justify-content: center;
    margin-top: 16px;

    .detail-button {
        width: auto;
        padding-left: 10px;
        padding-right: 10px;
        height: 36px;
        background: #00a8ff;
        color: #ffffff;
        border: none;
        border-radius: 4px;
        font-size: 14px;
        cursor: pointer;
        transition: background-color 0.3s;

        &:hover {
            background: #0088cc;
        }
    }
}
</style>