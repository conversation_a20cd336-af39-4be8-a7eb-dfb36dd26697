<template>
  <div class="gas-detector-alarm">
    <!-- 标题区域 -->
    <div class="header">
      <h1 class="title">{{state.deviceInfo?.deviceTypeName || ""}}</h1>
      <div class="alarm-status"
           :style="{ color: state.alarmInfo?.alarmLevel ? alarmLevelColorMap[state.alarmInfo?.alarmLevel]: '#ffffff'}">
        <span class="alarm-dot">●</span>
        <span class="alarm-text">{{state.alarmInfo?.alarmLevelName || ""}}</span>
      </div>
    </div>

    <!-- 浓度信息 -->
    <div class="concentration-info">
      <span class="label">{{state.alarmInfo?.monitorIndexName? state.alarmInfo?.monitorIndexName +"：" : ""}}</span>
      <span class="value">
          {{state.alarmInfo?.monitorIndexName.includes("状态") && state.alarmInfo?.alarmValue === "1" ? "异常" : state.alarmInfo?.alarmValue || "" }}
          {{state.alarmInfo?.alarmValueUnit ? "（" + state.alarmInfo?.alarmValueUnit + "）" : ""}}</span>
    </div>

    <!-- 位置信息 -->
    <div class="concentration-info">
      <span class="label">位置：</span>
      <span class="value">{{state.deviceInfo?.address || ""}}</span>
    </div>

    <!-- 确认按钮 -->
    <div class="button-container">
      <button class="confirm-btn" @click="handleModelAnalysis">确定分析</button>
    </div>
  </div>
</template>

<script setup>
import {reactive, watch} from "vue";
import {mapStates} from "@/components/GisMap/mapStates.js";
import {popupApiInfo, popupMonitorAlarmApiInfo} from "@/components/GisMap/popup/popupApi.js";
import {alarmLevelColorMap} from "@/components/GisMap/popup/device.js";
import bus from "@/utils/mitt.js";

const props = defineProps({
    data: {
        type: Object,
        default: () => ({}),
    },
    closeEvent: {
        type: Function,
        required: false
    }
});

const state = reactive({
    deviceInfo: null, //设备信息
    alarmInfo: null, //报警信息
});

const getDeviceInfo = () => {
    state.deviceInfo = null;
    if (popupApiInfo[props.data?.layerId]) {
        popupApiInfo[props.data?.layerId](props.data?.id).then(res => {
            state.deviceInfo = res?.data;
        });
    }
}

const getDeviceAlarmInfo = () => {
    state.alarmInfo = null;
    if (popupMonitorAlarmApiInfo[props.data?.layerId]){
        popupMonitorAlarmApiInfo[props.data?.layerId](props.data?.alarmId).then(res=>{
            state.alarmInfo = res?.data;
        })
    }
}

const handleModelAnalysis = () => {
    //跳转模型分析
    bus.emit("screenModelAnalysis", props.data)
    handleClose();
};

const handleClose = () => {
    props.closeEvent?.();
    //清除高亮
    mapStates.earth.entity.clearHighlight();
};

watch(
    () => props.data,
    () => {
        getDeviceInfo();
        getDeviceAlarmInfo();
    },
    {
        deep: true,
        immediate: true,
    }
);
</script>

<style scoped>
.gas-detector-alarm {
  background: rgba(11, 30, 65, 0.8);
  color: white;
  padding: 20px;
  border-radius: 8px;
  backdrop-filter: blur(10px);
  border: 1px solid #19385c;
  min-width: 300px;
  font-family: 'Microsoft YaHei', sans-serif;
}

.header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 10px;
}

.title {
  font-size: 16px;
  font-weight: bold;
  margin: 0;
  color: white;
}

.alarm-status {
  display: flex;
  align-items: center;
  padding-left: 30px;
  gap: 5px;
}

.alarm-dot {
  font-size: 20px;
  animation: pulse 2s infinite;
}

.alarm-text {
  font-size: 14px;
  font-weight: bold;
}

.concentration-info {
  margin-top: 5px;
  display: flex;
  align-items: center;
  gap: 5px;
}

.label {
  color: #cccccc;
  font-size: 14px;
}

.value {
  color: #cccccc;
  font-size: 14px;
}

.button-container {
  display: flex;
  justify-content: center;
    padding-top: 10px;
}

.confirm-btn {
  background: linear-gradient(135deg, #409eff 0%, #1890ff 100%);
  color: white;
  border: none;
  padding: 6px 10px;
  border-radius: 6px;
  font-size: 14px;
  font-weight: bold;
  cursor: pointer;
  box-shadow: 0 4px 8px rgba(74, 144, 226, 0.3);
  transition: all 0.3s ease;
  /* min-width: 120px; */
}

.confirm-btn:hover {
  background: linear-gradient(135deg, #66b1ff 0%, #40a9ff 100%);
  box-shadow: 0 4px 12px rgba(64, 158, 255, 0.3);
  transform: translateY(-2px);
}

.confirm-btn:active {
  transform: translateY(0);
  box-shadow: 0 4px 12px rgba(64, 158, 255, 0.3);
}

@keyframes pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
}
</style>