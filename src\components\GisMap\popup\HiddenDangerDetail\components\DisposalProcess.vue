<!-- 报警处置 -->
<template>
  <div class="device-process">
    <el-timeline class="timeline screen-table-style custom-table-card">
      <el-timeline-item
              v-for="(activity, index) in activities"
              :key="index"
              :type="activity.type"
              :size="activity.size"
              :hollow="activity.hollow"
      >
          <div class="activities-content">
              <h4>{{ activity.title }}</h4>
              <p v-if="activity.content">{{ activity.content }}</p>
              <p v-if="activity.onSchedule">是否按照计划：{{ activity.onSchedule }}</p>
              <p v-if="activity.extra.handleStatus">整改状态：{{ activity.extra.handleStatus }}</p>
              <p v-if="activity.extra.handleUser">操作人员：{{ activity.extra.handleUser }}</p>
              <p>{{ activity.timestamp }}</p>
          </div>
          <div v-if="activity.picUrls" class="timeline-images">
              <el-image
                      v-for="(url, imgIndex) in activity.picUrls.split(',')"
                      :key="imgIndex"
                      :src="url"
                      class="timeline-image"
                      preview-teleported
                      :zoom-rate="1.2"
                      :max-scale="7"
                      :min-scale="0.2"
                      fit="cover"
                      :preview-src-list="[url]"
              />
          </div>
          <div v-if="activity.remark" class="timeline-remark">
              备注：{{ activity.remark }}
          </div>
      </el-timeline-item>
    </el-timeline>
  </div>
</template>

<script setup>
import {ref, watch} from "vue";
import {popupMonitorAlarmStatusListApiInfo} from "@/components/GisMap/popup/popupApi.js";

const props = defineProps({
    //弹框标题
    data: {
        type: Object,
        default: () => ({}),
    },
});

// 时间轴数据
const activities = ref([]);

// 获取状态类型
const getStatusType = (status) => {
    const typeMap = {
        '7003401': 'danger', // 上报
        '7003402': 'warning', // 整改
        '7003403': 'info', // 复核
        '7003501': 'warning', // 整改中
        '7003502': 'success', // 整改完成
    };
    return typeMap[status] || 'info';
};

// 报警处理信息
const getAlarmDeal = async () => {
    if (popupMonitorAlarmStatusListApiInfo[props.data?.layerId]) {
        popupMonitorAlarmStatusListApiInfo[props.data?.layerId]({ dangerId: props.data?.id }).then(res => {
            activities.value = [];
            if (res?.data) {
                res?.data.map((item) => {
                    activities.value.push({
                        title: item?.processStatusName,
                        content: item?.description || '',
                        timestamp: item?.dealTime,
                        type: getStatusType(item?.processStatus),
                        size: 'normal',
                        hollow: false,
                        picUrls: item?.picUrls,
                        remark: item?.remark,
                        onSchedule: item?.onSchedule ? '是' : '否',
                        extra: {
                            handleStatus: item?.handleStatusName,
                            handleUser: item?.handleUserName,
                        }
                    })
                })
            }
        })
    }
};
watch(
    () => props.data,
    () => {
        getAlarmDeal();
    },
    {
        deep: true,
        immediate: true,
    }
);
</script>
<style lang="scss" scoped>
.device-process {
  height: 360px;
  overflow-y: auto;
  display: flex;
  flex-direction: column;

  .timeline {
    flex: 1;
    overflow: auto;
    padding: 0 15px;
  }
  :deep(.el-timeline-item__timestamp) {
    color: #fff;
    font-size: 14px;
    letter-spacing: 2px;
  }
  :deep(.el-timeline-item__tail) {
    border-color: #19385c;
  }
}
.file-icon {
  width: 32px;
  height: 26px;
  position: relative;
  top: 7px;
  left: 10px;
}
.file-list {
  .file-item {
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    cursor: pointer;
    font-size: 15px;
    margin-bottom: 5px;
    &:hover {
      color: var(--el-color-primary);
    }
  }
}
.dot {
  background: #fff;
  width: 20px;
  height: 20px;
  border-radius: 10px;
  margin-left: -5px;
  background: #19385c;
  position: relative;
  &::after {
    content: "";
    position: absolute;
    width: 7px;
    height: 7px;
    background: #ffffff;
    border-radius: 20px;
    left: 50%;
    top: 50%;
    transform: translate(-50%, -50%);
  }
}
:deep(.isCurrent.el-timeline-item) {
  .dot::after {
    background: var(--el-color-primary);
  }
  .el-timeline-item__timestamp {
    font-weight: bold;
  }
}
:deep(.isAfter.el-timeline-item) {
  .el-timeline-item__timestamp {
    color: rgba(255, 255, 255, 0.8);
  }
}

.btn-group {
  padding: 10px 10px 0;
  display: flex;
  align-items: center;
  justify-content: flex-end;
}

.activities-content {
  color: rgba(255, 255, 255, 0.8);;
}

.timeline-images {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
  margin-bottom: 12px;
}

.timeline-image {
  width: 60px;
  height: 60px;
  border-radius: 4px;
  border: 1px solid #ddd;
}

.timeline-remark {
  color: #666;
  font-size: 12px;
  font-style: italic;
}

</style>

<style>
.file-popover.el-popover.el-popper {
  .el-popover__title {
    color: #000;
    font-size: 17px;
  }
}
.el-table__cell.dealFile {
  padding: 0;
}
</style>
