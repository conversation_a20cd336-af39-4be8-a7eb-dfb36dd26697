import request from '@/utils/request'
import moment from 'moment'

/**
 * 分页查询登陆日志
 * @param {number} page 页码
 * @param {number} size 每页大小
 * @param {object} params 查询参数
 * @returns {Promise}
 */
export function getLoginLogPage(page, size, params = {}) {
  return request({
    url: `/sys/log/search/${page}/${size}`,
    method: 'post',
    data: params
  })
}

/**
 * 获取登陆日志详情
 * @param {string} id 日志ID
 * @returns {Promise}
 */
export function getLoginLogDetail(id) {
  return request({
    url: `/sys/log/${id}`,
    method: 'get'
  })
}

// 角色管理相关接口

/**
 * 分页查询角色列表
 * @param {number} page 页码
 * @param {number} size 每页大小
 * @param {object} params 查询参数
 * @returns {Promise}
 */
export function getRolePage(page, size, params = {}) {
  return request({
    url: `/sys/role/search/${page}/${size}`,
    method: 'post',
    data: params
  })
}

/**
 * 保存角色
 * @param {object} data 角色数据
 * @returns {Promise}
 */
export function saveRole(data) {
  return request({
    url: `/sys/role`,
    method: 'post',
    data
  })
}

/**
 * 更新角色
 * @param {object} data 角色数据
 * @returns {Promise}
 */
export function updateRole(data) {
  return request({
    url: `/sys/role`,
    method: 'post',
    data
  })
}

/**
 * 删除角色
 * @param {string} id 角色ID
 * @returns {Promise}
 */
export function deleteRole(id) {
  return request({
    url: `/sys/role/${id}`,
    method: 'delete'
  })
}

/**
 * 获取所有权限树
 * @returns {Promise}
 */
export function getPermissionTree() {
  return request({
    url: `/sys/permission/tree`,
    method: 'get'
  })
}

/**
 * 给指定角色设置权限
 * @param {object} data 权限数据 {permissions: [], roleId: ''}
 * @returns {Promise}
 */
export function distributePermission(data) {
  return request({
    url: `/sys/role/distribute-permission`,
    method: 'post',
    data
  })
}

/**
 * 给指定角色设置数据权限
 * @param {object} data 数据权限 {dataScope: '', depts: [], roleId: ''}
 * @returns {Promise}
 */
export function distributeDataScope(data) {
  return request({
    url: `/sys/role/distribute-data-scope`,
    method: 'post',
    data
  })
}

/**
 * 获取所有部门树
 * @returns {Promise}
 */
export function getDeptTree() {
  return request({
    url: `/sys/dept/tree`,
    method: 'get'
  })
}

// /sys/dept/search post
export function getDeptListTree(params) {
  return request({
    url: `/sys/dept/search`,
    method: 'post',
    data: params
  })
}

/**
 * 获取指定角色所有权限
 * @param {string} roleId 角色ID
 * @returns {Promise}
 */
export function getRolePermissions(roleId) {
  return request({
    url: `/sys/role/permission/${roleId}`,
    method: 'get'
  })
}

/**
 * 用户条件查询
 * @param {object} params 查询参数 {roleId: ''}
 * @returns {Promise}
 */
export function searchUsers(params) {
  return request({
    url: `/sys/user/search`,
    method: 'post',
    data: params
  })
}

// 组织管理相关接口

/**
 * 保存部门信息
 * @param {object} data 部门数据
 * @returns {Promise}
 */
export function saveDept(data) {
  // 处理时间字段格式
  const submitData = { ...data }
  if (submitData.createTime) {
    submitData.createTime = moment(submitData.createTime).format('YYYY-MM-DD HH:mm:ss')
  }
  if (submitData.updateTime) {
    submitData.updateTime = moment(submitData.updateTime).format('YYYY-MM-DD HH:mm:ss')
  }
  
  return request({
    url: `/sys/dept`,
    method: 'post',
    data: submitData
  })
}

/**
 * 根据Id查询部门详情
 * @param {string} id 部门ID
 * @returns {Promise}
 */
export function getDeptDetail(id) {
  return request({
    url: `/sys/dept/${id}`,
    method: 'get'
  })
}

/**
 * 删除部门
 * @param {string} id 部门ID
 * @returns {Promise}
 */
export function deleteDept(id) {
  return request({
    url: `/sys/dept/${id}`,
    method: 'delete'
  })
}

/**
 * 用户条件分页查询
 * @param {number} page 页码
 * @param {number} size 每页大小
 * @param {object} params 查询参数
 * @returns {Promise}
 */
export function getUserPage(page, size, data) {
  return request({
    url: `/sys/user/search/${page}/${size}`,
    method: 'post',
    data: data
  })
}

/**
 * 保存用户
 * @param {object} data 用户数据
 * @returns {Promise}
 */
export function saveUser(data) {
  // 处理时间字段格式
  const submitData = { ...data }
  if (submitData.createTime) {
    submitData.createTime = moment(submitData.createTime).format('YYYY-MM-DD HH:mm:ss')
  }
  if (submitData.updateTime) {
    submitData.updateTime = moment(submitData.updateTime).format('YYYY-MM-DD HH:mm:ss')
  }
  
  return request({
    url: `/sys/user`,
    method: 'post',
    data: submitData
  })
}

/**
 * 密码重置
 * @param {string} id 用户ID
 * @returns {Promise}
 */
export function resetUserPassword(id) {
  return request({
    url: `/sys/user/reset/password/${id}`,
    method: 'post'
  })
}

/**
 * 根据Id查询用户详情
 * @param {string} id 用户ID
 * @returns {Promise}
 */
export function getUserDetail(id) {
  return request({
    url: `/sys/user/${id}`,
    method: 'get'
  })
}

/**
 * 删除用户
 * @param {string} id 用户ID
 * @returns {Promise}
 */
export function deleteUser(id) {
  return request({
    url: `/sys/user/${id}`,
    method: 'delete'
  })
}
export function getAllUsers() {
  return request({
    url: `/sys/user`,
    method: 'get'
  })
}
/**
 * 获取所有角色列表
 * @returns {Promise}
 */
export function getAllRoles() {
  return request({
    url: `/sys/role`,
    method: 'get'
  })
}

// 用户状态常量
export const USER_STATE_OPTIONS = [
  { label: '正常', value: '0' },
  { label: '停用', value: '1' }
]
