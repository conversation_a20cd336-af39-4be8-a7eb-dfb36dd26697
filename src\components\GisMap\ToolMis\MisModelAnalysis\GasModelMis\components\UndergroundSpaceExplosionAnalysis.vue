<template>
    <div class="mis-underground-space-explosion-analysis">
        <div class="analysis-header">
            <div class="title">爆炸分析</div>
            <div class="close-btn" @click="$emit('close')">
                <el-icon>
                    <Close/>
                </el-icon>
            </div>
        </div>

        <div class="content-area">
            <!-- 报警信息 -->
            <div class="alarm-section">
                <div class="section-header">
                    <div class="section-title">报警信息</div>

                    <!-- 设备信息和监测曲线标签页 -->
                    <div class="tab-container">
                        <div
                            class="tab-item"
                            :class="{ active: activeTab === 'device' }"
                            @click="activeTab = 'device'"
                        >
                            设备信息
                        </div>
                        <div
                            class="tab-item"
                            :class="{ active: activeTab === 'curve' }"
                            @click="activeTab = 'curve'"
                        >
                            监测曲线
                        </div>
                    </div>
                </div>

                <!-- 设备信息内容 -->
                <div v-show="activeTab === 'device'" class="device-info">
                    <div class="info-row">
                        <span class="label">设备名称：</span>
                        <span class="value">{{ state.deviceInfo?.deviceName || "" }}</span>
                    </div>
                    <div class="info-row">
                        <span class="label">设备编码：</span>
                        <span class="value">{{ state.deviceInfo?.indexCode || "" }}</span>
                    </div>
                    <div class="info-row">
                        <span class="label">报警类型：</span>
                        <span class="value">{{ state.alarmInfo?.alarmTypeName || "" }}</span>
                    </div>
                    <div class="info-row">
                        <span class="label">报警等级：</span>
                        <span class="value alarm-level"
                              :style="{ color: state.alarmInfo?.alarmLevel ? alarmLevelColorMap[state.alarmInfo?.alarmLevel]: '#ffffff'}"
                        >{{ state.alarmInfo?.alarmLevelName || "" }}</span>
                    </div>
                    <div class="info-row">
                        <span class="label">报警值：</span>
                        <span class="value alarm-value"
                              :style="{ color: state.alarmInfo?.alarmLevel ? alarmLevelColorMap[state.alarmInfo?.alarmLevel]: '#ffffff'}"
                        >
                          {{
                                state.alarmInfo?.monitorIndexName.includes("状态") && state.alarmInfo?.alarmValue === "1" ? "异常" : state.alarmInfo?.alarmValue || ""
                            }}
                          {{ state.alarmInfo?.alarmValueUnit ? "（" + state.alarmInfo?.alarmValueUnit + "）" : "" }}
                        </span>
                    </div>
                    <div class="info-row">
                        <span class="label">监测对象：</span>
                        <span class="value">
                            {{ state.deviceInfo?.monitorObjectName || "" }}
                            {{ state.deviceInfo?.monitorTargetName ? `(${state.deviceInfo?.monitorTargetName})` : "" }}
                        </span>
                    </div>
                    <div class="info-row">
                        <span class="label">设备位置：</span>
                        <span class="value">{{ state.deviceInfo?.address || "" }}</span>
                    </div>
                </div>

                <!-- 监测曲线内容 -->
                <div v-show="activeTab === 'curve'" class="curve-info">
                    <MonitorCurve :data="data"/>
                </div>
            </div>

            <!-- 影响范围 -->
            <div class="impact-range-section">
                <div class="section-header">
                    <div class="section-title">影响范围</div>
                </div>
                <div class="impact-list">
                    <div class="impact-item">
                        <div class="impact-icon red"></div>
                        <span class="impact-text">伤害影响面积：2万m²</span>
                    </div>
                </div>
            </div>

            <!-- 危险源 -->
            <div class="hazard-section">
                <div class="section-header">
                    <div class="section-title">危险源</div>
                    <div class="filter-buttons">
                        <div
                            class="filter-btn"
                            :class="{ active: activeHazardFilter === 'diffusion' }"
                            @click="activeHazardFilter = 'diffusion'"
                        >
                            扩散范围内
                        </div>
                        <div
                            class="filter-btn"
                            :class="{ active: activeHazardFilter === 'surrounding' }"
                            @click="activeHazardFilter = 'surrounding'"
                        >
                            周边1公里内
                        </div>
                    </div>
                </div>
                <div class="table-container">
                    <el-table
                        :data="hazardData"
                        class="data-table dark-table"
                        :border="false"
                        size="small"
                    >
                        <el-table-column prop="name" label="危险源名称" width="120"/>
                        <el-table-column prop="buildingType" label="建筑物类型" width="100"/>
                        <el-table-column prop="contact" label="联系人" width="80"/>
                        <el-table-column prop="phone" label="联系电话" width="100"/>
                        <el-table-column label="操作" width="120">
                            <template #default="scope">
                                <div class="action-buttons">
                                    <span class="action-btn">定位</span>
                                </div>
                            </template>
                        </el-table-column>
                    </el-table>
                </div>
            </div>

            <!-- 防护目标 -->
            <div class="protection-section">
                <div class="section-header">
                    <div class="section-title">防护目标</div>
                    <div class="filter-buttons">
                        <div
                            class="filter-btn"
                            :class="{ active: activeProtectionFilter === 'diffusion' }"
                            @click="activeProtectionFilter = 'diffusion'"
                        >
                            扩散范围内
                        </div>
                        <div
                            class="filter-btn"
                            :class="{ active: activeProtectionFilter === 'surrounding' }"
                            @click="activeProtectionFilter = 'surrounding'"
                        >
                            周边1公里内
                        </div>
                    </div>
                </div>
                <div class="table-container">
                    <el-table
                        :data="protectionData"
                        class="data-table dark-table"
                        :border="false"
                        size="small"
                    >
                        <el-table-column prop="name" label="防护目标名称" width="120"/>
                        <el-table-column prop="buildingType" label="建筑物类型" width="100"/>
                        <el-table-column prop="contact" label="联系人" width="80"/>
                        <el-table-column prop="phone" label="联系电话" width="100"/>
                        <el-table-column label="操作" width="120">
                            <template #default="scope">
                                <div class="action-buttons">
                                    <span class="action-btn">定位</span>
                                </div>
                            </template>
                        </el-table-column>
                    </el-table>
                </div>
            </div>
        </div>
    </div>
</template>

<script setup>
import {onMounted, reactive, ref, watch} from 'vue'
import {Close} from '@element-plus/icons-vue'
import {popupApiInfo, popupMonitorAlarmApiInfo} from "@/components/GisMap/popup/popupApi.js";
import {alarmLevelColorMap} from "@/components/GisMap/popup/device.js";
import MonitorCurve from "./MonitorCurve.vue";

const props = defineProps({
    data: {
        type: Object,
        default: () => ({}),
    }
})

const state = reactive({
    deviceInfo: null, //设备信息
    alarmInfo: null, //报警信息
});

// 危险源数据
const hazardData = ref([
    {
        name: '走官营村',
        buildingType: '',
        contact: '',
        phone: ''
    },
    {
        name: '',
        buildingType: '',
        contact: '',
        phone: ''
    },
    {
        name: '',
        buildingType: '',
        contact: '',
        phone: ''
    }
]);

// 防护目标数据
const protectionData = ref([
    {
        name: '',
        buildingType: '',
        contact: '',
        phone: ''
    },
    {
        name: '',
        buildingType: '',
        contact: '',
        phone: ''
    }
]);

const emit = defineEmits(['close']);
const activeTab = ref('device');
const activeHazardFilter = ref('diffusion');
const activeProtectionFilter = ref('diffusion');

const getDeviceInfo = () => {
    state.deviceInfo = null;
    if (popupApiInfo[props.data?.layerId]) {
        popupApiInfo[props.data?.layerId](props.data?.id).then(res => {
            state.deviceInfo = res?.data;
        });
    }
}

const getDeviceAlarmInfo = () => {
    state.alarmInfo = null;
    if (popupMonitorAlarmApiInfo[props.data?.layerId]) {
        popupMonitorAlarmApiInfo[props.data?.layerId](props.data?.alarmId).then(res => {
            state.alarmInfo = res?.data;
        })
    }
}

onMounted(() => {
    getDeviceInfo();
    getDeviceAlarmInfo();
});
</script>

<style lang="scss" scoped>
.mis-underground-space-explosion-analysis {
  position: fixed;
  top: 18vh;
  right: 2vw;
  width: 400px;
  display: flex;
  flex-direction: column;
  border-radius: 8px;
  pointer-events: all;

  &::before {
    content: "";
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    backdrop-filter: blur(10px);
    background: rgba(255, 255, 255, 1);
    border-radius: 8px;
    border: 1px solid rgba(255, 255, 255, 0.4);
    z-index: -1;
  }

  .analysis-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    border-top-left-radius: 12px;
    padding: 10px 20px;
    font-size: 16px;
    font-weight: bold;
    color: rgba(50, 50, 50, 0.8);
    border-bottom: 1px solid rgba(200, 200, 200, 0.4);

    .title {
      font-size: 16px;
      font-weight: bold;
      color: rgba(50, 50, 50, 0.8);
    }

    .close-btn {
      font-size: 20px;
      cursor: pointer;
      color: rgba(50, 50, 50, 0.8);

      &:hover {
        opacity: 0.8;
      }
    }
  }

  .content-area {
    flex: 1;
    padding: 10px 20px;
    overflow: auto;

    .alarm-section, .impact-range-section, .hazard-section, .protection-section {
      margin-bottom: 20px;
    }

    .alarm-section {
      .section-header {
        display: flex;
        align-items: center;
        justify-content: space-between;
        margin-bottom: 10px;
        border-bottom: 1px solid rgba(255, 255, 255, 0.8);
        padding-bottom: 10px;

        .section-title {
          font-size: 14px;
          font-weight: bold;
          color: rgba(50, 50, 50, 0.8);
        }

        .tab-container {
          display: flex;
          align-items: center;

          .tab-item {
            color: rgba(50, 50, 50, 0.8);
            font-size: 13px;
            letter-spacing: 1px;
            position: relative;
            margin-left: 20px;
            cursor: pointer;

            &::after {
              content: "";
              position: absolute;
              left: 50%;
              transform: translateX(-50%);
              bottom: -10px;
              width: 0;
              height: 2px;
              background: #4a9eff;
              opacity: 0;
              border-radius: 1px;
              transition: all 0.2s ease;
            }

            &.active {
              position: relative;
              color: #4a9eff;

              &:after {
                opacity: 1;
                width: 40px;
              }
            }
          }
        }
      }

      .device-info {
        .info-row {
          display: flex;
          margin-bottom: 8px;

          .label {
            color: rgba(50, 50, 50, 0.8);
            font-size: 12px;
            min-width: 70px;
            text-align: right;
          }

          .value {
            color: rgba(50, 50, 50, 0.8);
            font-size: 12px;

            &.alarm-level, &.alarm-value {
              color: #ff4757;
              font-weight: bold;
            }
          }
        }
      }

      .curve-info {
        .curve-placeholder {
          height: 120px;
          display: flex;
          align-items: center;
          justify-content: center;
          color: rgba(50, 50, 50, 0.8);
          font-size: 12px;
          border: 1px dashed rgba(255, 255, 255, 0.4);
          border-radius: 4px;
        }
      }
    }

    .impact-range-section {
      .section-header {
        display: flex;
        align-items: center;
        justify-content: space-between;
        margin-bottom: 10px;
        border-bottom: 1px solid rgba(255, 255, 255, 0.8);
        padding-bottom: 10px;

        .section-title {
          font-size: 14px;
          font-weight: bold;
          color: rgba(50, 50, 50, 0.8);
        }
      }
      .impact-list {
        display: flex;
        flex-direction: column;
        gap: 8px;
        .impact-item {
          display: flex;
          align-items: center;
          .impact-icon {
            width: 16px;
            height: 16px;
            border-radius: 50%;
            margin-right: 8px;
            &.red {
              background-color: #ff4757;
            }
            &.orange {
              background-color: #ffa502;
            }
            &.yellow {
              background-color: #ffd32a;
            }
            &.blue {
              background-color: #1775f9;
            }
          }
          .impact-text {
            color: rgba(50, 50, 50, 0.8);
            font-size: 12px;
          }
        }
      }
    }

    .hazard-section, .protection-section {
      .section-header {
        display: flex;
        align-items: center;
        justify-content: space-between;
        margin-bottom: 10px;
        border-bottom: 1px solid rgba(255, 255, 255, 0.8);
        padding-bottom: 10px;
        .section-title {
          font-size: 14px;
          font-weight: bold;
          color: rgba(50, 50, 50, 0.8);
        }
        .filter-buttons {
          display: flex;
          align-items: center;
          gap: 20px;
          .filter-btn {
            color: rgba(50, 50, 50, 0.8);
            font-size: 13px;
            letter-spacing: 1px;
            position: relative;
            cursor: pointer;
            &.active {
              position: relative;
              color: #4a9eff;
              &:after {
                opacity: 1;
                width: 40px;
              }
            }
            &:after {
              content: "";
              position: absolute;
              left: 50%;
              transform: translateX(-50%);
              bottom: -10px;
              width: 0;
              height: 2px;
              background: #4a9eff;
              opacity: 0;
              border-radius: 1px;
              transition: all 0.2s ease;
            }
          }
        }
      }
        .table-container {
            .data-table {
                width: 100%;
                color: rgba(50, 50, 50, 0.8);
                font-size: 12px;

                // 覆盖 el-table 的默认样式
                :deep(.el-table) {
                    background-color: transparent;
                    color: rgba(50, 50, 50, 0.8);

                    .el-table__header-wrapper {
                        .el-table__header {
                            background-color: transparent;

                            th {
                                background-color: transparent !important;
                                color: rgba(50, 50, 50, 0.8) !important;
                                border-bottom: 1px solid rgba(50, 50, 50, 0.2) !important;
                            }
                        }
                    }

                    .el-table__body-wrapper {
                        .el-table__body {
                            background-color: transparent;

                            tr {
                                background-color: transparent !important;

                                td {
                                    background-color: transparent !important;
                                    color: rgba(50, 50, 50, 0.8) !important;
                                    border-bottom: 1px solid rgba(50, 50, 50, 0.2) !important;
                                }

                                &:last-child {
                                    td {
                                        border-bottom: none !important;
                                    }
                                }
                            }
                        }
                    }
                }

                .action-buttons {
                    display: flex;
                    gap: 12px;

                    .action-btn {
                        color: #409eff;
                        cursor: pointer;
                        font-size: 12px;
                        transition: all 0.2s ease;

                        &:hover {
                            color: #66b1ff;
                        }
                    }
                }
            }

            // 深色表格专用样式 - 独立的作用域
            .dark-table {
                :deep(.el-table) {
                    background-color: transparent !important;

                    // 表格边框
                    &::before {
                        display: none !important;
                    }

                    .el-table__border-line {
                        display: none !important;
                    }

                    // 表头样式 - 使用指定的背景色
                    .el-table__header-wrapper {
                        .el-table__header {
                            background-color: rgba(50, 50, 50, 0) !important;

                            th {
                                background-color: rgba(50, 50, 50, 0.2) !important;
                                color: rgba(50, 50, 50, 0.8) !important;
                                border-bottom: 1px solid rgba(50, 50, 50, 0.2) !important;
                                border-right: 1px solid rgba(50, 50, 50, 0.2) !important;
                                //font-weight: 500;
                                padding: 8px 12px;

                                &.is-leaf {
                                    border-bottom: 1px solid rgba(50, 50, 50, 0.8) !important;
                                }

                                &:last-child {
                                    border-right: none !important;
                                }

                                .cell {
                                    color: rgba(50, 50, 50, 0.2) !important;
                                }
                            }
                        }
                    }

                    // 表格主体样式
                    .el-table__body-wrapper {
                        .el-table__body {
                            background-color: transparent !important;

                            tr {
                                background-color: rgba(50, 50, 50, 0.2) !important;

                                &:hover {
                                    background-color: rgba(50, 50, 50, 0.2) !important;
                                }

                                td {
                                    background-color: rgba(50, 50, 50, 0) !important;
                                    color: rgba(50, 50, 50, 0.8) !important;
                                    border-bottom: 1px solid rgba(50, 50, 50, 0.2) !important;
                                    border-right: 1px solid rgba(50, 50, 50, 0.2) !important;
                                    padding: 8px 12px;

                                    .cell {
                                        color: rgba(50, 50, 50, 0.8) !important;
                                    }

                                    &:last-child {
                                        border-right: none !important;
                                    }
                                }

                                &:last-child {
                                    td {
                                        border-bottom: none !important;
                                    }
                                }
                            }
                        }
                    }

                    // 空数据样式
                    .el-table__empty-block {
                        background-color: transparent !important;

                        .el-table__empty-text {
                            color: rgba(50, 50, 50, 0.8) !important;
                        }
                    }
                }
            }

            // 额外的强制样式，确保优先级
            .dark-table.el-table {
                background-color: transparent !important;

            }

            .dark-table .el-table__header-wrapper .el-table__header {
                background-color: rgba(50, 50, 50, 0) !important;
            }

            .dark-table .el-table__header-wrapper .el-table__header th {
                background-color: rgba(50, 50, 50, 0) !important;
                color: rgba(50, 50, 50, 0.8) !important;
                border-bottom: 1px solid rgba(50, 50, 50, 0.2) !important;
                border-right: 1px solid rgba(50, 50, 50, 0.2) !important;
            }

            .dark-table .el-table__body-wrapper .el-table__body tr {
                background-color: rgba(50, 50, 50, 0) !important;
            }

            .dark-table .el-table__body-wrapper .el-table__body tr td {
                background-color: rgba(50, 50, 50, 0) !important;
                color: rgba(50, 50, 50, 0.8) !important;
                border-bottom: 1px solid rgba(50, 50, 50, 0.2) !important;
                border-right: 1px solid rgba(50, 50, 50, 0.2) !important;
            }
        }
    }
  }

    // 全局样式覆盖，确保深色表格样式生效
    :deep(.dark-table .el-table) {
        background-color: transparent !important;
    }

    :deep(.dark-table .el-table__header-wrapper .el-table__header) {
        background-color: rgba(50, 50, 50, 0) !important;
    }

    :deep(.dark-table .el-table__header-wrapper .el-table__header th) {
        background-color: rgba(50, 50, 50, 0) !important;
        color: rgba(50, 50, 50, 0.8) !important;
        border: 1px solid rgba(50, 50, 50, 0.2) !important;
    }

    :deep(.dark-table .el-table__body-wrapper .el-table__body tr) {
        background-color: rgba(50, 50, 50, 0) !important;
    }

    :deep(.dark-table .el-table__body-wrapper .el-table__body tr td) {
        background-color: rgba(50, 50, 50, 0) !important;
        color: rgba(50, 50, 50, 0.8) !important;
        border: 1px solid rgba(50, 50, 50, 0.2) !important;
    }

    :deep(.el-table) {
        --el-table-border-color: rgba(11, 30, 65, 0) !important;
    }
}
</style>