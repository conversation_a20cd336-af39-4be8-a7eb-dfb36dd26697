<template>
    <div class="mis-leakage-analysis">
        <div class="analysis-header">
            <div class="title">溯源分析</div>
            <div class="close-btn" @click="$emit('close')">
                <el-icon>
                    <Close/>
                </el-icon>
            </div>
        </div>

        <div class="content-area">
            <!-- 报警信息 -->
            <div class="alarm-section">
                <div class="section-header">
                    <div class="section-title">报警信息</div>

                    <!-- 设备信息和监测曲线标签页 -->
                    <div class="tab-container">
                        <div
                                class="tab-item"
                                :class="{ active: activeTab === 'device' }"
                                @click="activeTab = 'device'"
                        >
                            设备信息
                        </div>
                        <div
                                class="tab-item"
                                :class="{ active: activeTab === 'curve' }"
                                @click="activeTab = 'curve'"
                        >
                            监测曲线
                        </div>
                    </div>
                </div>

                <!-- 设备信息内容 -->
                <div v-show="activeTab === 'device'" class="device-info">
                    <div class="info-row">
                        <span class="label">设备名称：</span>
                        <span class="value">{{ state.deviceInfo?.deviceName || "" }}</span>
                    </div>
                    <div class="info-row">
                        <span class="label">设备编码：</span>
                        <span class="value">{{ state.deviceInfo?.indexCode || "" }}</span>
                    </div>
                    <div class="info-row">
                        <span class="label">报警类型：</span>
                        <span class="value">{{ state.alarmInfo?.alarmTypeName || "" }}</span>
                    </div>
                    <div class="info-row">
                        <span class="label">报警等级：</span>
                        <span class="value alarm-level"
                              :style="{ color: state.alarmInfo?.alarmLevel ? alarmLevelColorMap[state.alarmInfo?.alarmLevel]: '#ffffff'}"
                        >{{ state.alarmInfo?.alarmLevelName || "" }}</span>
                    </div>
                    <div class="info-row">
                        <span class="label">报警值：</span>
                        <span class="value alarm-value"
                          :style="{ color: state.alarmInfo?.alarmLevel ? alarmLevelColorMap[state.alarmInfo?.alarmLevel]: '#ffffff'}"
                        >
                          {{state.alarmInfo?.monitorIndexName.includes("状态") && state.alarmInfo?.alarmValue === "1" ? "异常" : state.alarmInfo?.alarmValue || "" }}
                          {{state.alarmInfo?.alarmValueUnit ? "（" + state.alarmInfo?.alarmValueUnit + "）" : ""}}
                        </span>
                    </div>
                    <div class="info-row">
                        <span class="label">监测对象：</span>
                        <span class="value">
                            {{ state.deviceInfo?.monitorObjectName || "" }}
                            {{ state.deviceInfo?.monitorTargetName? `(${state.deviceInfo?.monitorTargetName})` : "" }}
                        </span>
                    </div>
                    <div class="info-row">
                        <span class="label">设备位置：</span>
                        <span class="value">{{ state.deviceInfo?.address || "" }}</span>
                    </div>
                </div>

                <!-- 监测曲线内容 -->
                <div v-show="activeTab === 'curve'" class="curve-info">
                   <MonitorCurve :data="data" />
                </div>
            </div>

            <!-- 可能泄漏管线 -->
            <div class="pipeline-section">
                <div class="section-title">可能泄漏管线</div>
                <div class="pipeline-list">
                    <div
                            v-for="pipeline in state.pipelines"
                            :key="pipeline.id"
                            class="pipeline-item"
                            @click="handlePipelineClick(pipeline)"
                    >
                        {{ pipeline.name }}
                    </div>
                </div>
            </div>
        </div>
    </div>
</template>

<script setup>
import {onMounted, reactive, ref, watch} from 'vue'
import {Close} from '@element-plus/icons-vue'
import {popupApiInfo, popupMonitorAlarmApiInfo} from "@/components/GisMap/popup/popupApi.js";
import {alarmLevelColorMap} from "@/components/GisMap/popup/device.js";
import MonitorCurve from "./MonitorCurve.vue";

const props = defineProps({
    data: {
        type: Object,
        default: () => ({}),
    }
})

const state = reactive({
    deviceInfo: null, //设备信息
    alarmInfo: null, //报警信息
    pipelines: [
        {id: 1, name: '管线*********'},
        {id: 2, name: '管线*********'}
    ]
});

const emit = defineEmits(['close']);
const activeTab = ref('device');

const getDeviceInfo = () => {
    state.deviceInfo = null;
    if (popupApiInfo[props.data?.layerId]) {
        popupApiInfo[props.data?.layerId](props.data?.id).then(res => {
            state.deviceInfo = res?.data;
        });
    }
}

const getDeviceAlarmInfo = () => {
    state.alarmInfo = null;
    if (popupMonitorAlarmApiInfo[props.data?.layerId]) {
        popupMonitorAlarmApiInfo[props.data?.layerId](props.data?.alarmId).then(res => {
            state.alarmInfo = res?.data;
        })
    }
}

const handlePipelineClick = (pipeline) => {
    // todo 点击管线处理逻辑
}

onMounted(() => {
    getDeviceInfo();
    getDeviceAlarmInfo();
});
</script>

<style lang="scss" scoped>
.mis-leakage-analysis {
  position: fixed;
  top: 18vh;
  right: 2vw;
  width: 400px;
  display: flex;
  flex-direction: column;
  border-radius: 8px;
  pointer-events: all;

  &::before {
    content: "";
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    backdrop-filter: blur(10px);
    background: rgba(255, 255, 255, 1);
    border-radius: 8px;
    border: 1px solid rgba(255, 255, 255, 0.4);
    z-index: -1;
  }

  .analysis-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    border-top-left-radius: 12px;
    padding: 10px 20px;
    font-size: 16px;
    font-weight: bold;
    color: rgba(50, 50, 50, 0.8);
    border-bottom: 1px solid rgba(200, 200, 200, 0.4);

    .title {
      font-size: 16px;
      font-weight: bold;
      color: rgba(50, 50, 50, 0.8);
    }

    .close-btn {
      font-size: 20px;
      cursor: pointer;
      color: rgba(50, 50, 50, 0.8);

      &:hover {
        opacity: 0.8;
      }
    }
  }

  .content-area {
    flex: 1;
    padding: 10px 20px;
    overflow: auto;

    .alarm-section, .pipeline-section {
      margin-bottom: 20px;
    }

    .pipeline-section {
      .section-title {
        font-size: 14px;
        font-weight: bold;
        color: rgba(50, 50, 50, 0.8);
        margin-bottom: 12px;
      }
    }

    .alarm-section {
      .section-header {
        display: flex;
        align-items: center;
        justify-content: space-between;
        margin-bottom: 10px;
        border-bottom: 1px solid rgba(255, 255, 255, 0.8);
        padding-bottom: 10px;

        .section-title {
          font-size: 14px;
          font-weight: bold;
          color: rgba(50, 50, 50, 0.8);
        }

        .tab-container {
          display: flex;
          align-items: center;

          .tab-item {
            color: rgba(50, 50, 50, 0.8);
            font-size: 13px;
            letter-spacing: 1px;
            position: relative;
            margin-left: 20px;
            cursor: pointer;

            &::after {
              content: "";
              position: absolute;
              left: 50%;
              transform: translateX(-50%);
              bottom: -10px;
              width: 0;
              height: 2px;
              background: #4a9eff;
              opacity: 0;
              border-radius: 1px;
              transition: all 0.2s ease;
            }

            &.active {
              position: relative;
              color: #4a9eff;

              &:after {
                opacity: 1;
                width: 40px;
              }
            }
          }
        }
      }

      .device-info {
        .info-row {
          display: flex;
          margin-bottom: 8px;

          .label {
            color: rgba(50, 50, 50, 0.8);
            font-size: 12px;
            min-width: 70px;
            text-align: right;
          }

          .value {
            color: rgba(50, 50, 50, 0.8);
            font-size: 12px;

            &.alarm-level, &.alarm-value {
              color: #ff4757;
              font-weight: bold;
            }
          }
        }
      }

      .curve-info {
        .curve-placeholder {
          height: 120px;
          display: flex;
          align-items: center;
          justify-content: center;
          color: rgba(50, 50, 50, 0.8);
          font-size: 12px;
          border: 1px dashed rgba(255, 255, 255, 0.4);
          border-radius: 4px;
        }
      }
    }

    .pipeline-section {
      .pipeline-list {
        .pipeline-item {
          color: #4a9eff;
          font-size: 12px;
          cursor: pointer;
          margin-bottom: 6px;

          &:hover {
            text-decoration: underline;
          }
        }
      }
    }
  }
}
</style>