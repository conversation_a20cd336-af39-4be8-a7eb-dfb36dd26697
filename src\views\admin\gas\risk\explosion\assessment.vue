<template>
  <div class="gas-risk-explosion-assessment">
    <!-- 上部分区域：风险值统计 -->
    <div class="risk-stats-section">
      <div class="risk-card major-risk">
        <div class="risk-title">重大风险</div>
        <div class="risk-value">
          <span class="value">{{ riskLevelData['7001'] || 0 }}</span>
          <span class="unit">km</span>
        </div>
      </div>
      <div class="risk-card high-risk">
        <div class="risk-title">较大风险</div>
        <div class="risk-value">
          <span class="value">{{ riskLevelData['7002'] || 0 }}</span>
          <span class="unit">km</span>
        </div>
      </div>
      <div class="risk-card normal-risk">
        <div class="risk-title">一般风险</div>
        <div class="risk-value">
          <span class="value">{{ riskLevelData['7003'] || 0 }}</span>
          <span class="unit">km</span>
        </div>
      </div>
      <div class="risk-card low-risk">
        <div class="risk-title">低风险</div>
        <div class="risk-value">
          <span class="value">{{ riskLevelData['7004'] || 0 }}</span>
          <span class="unit">km</span>
        </div>
      </div>
    </div>

    <!-- 中部分区域：查询表单 -->
    <div class="search-section">
      <GasRiskSearch @search="handleSearch" @reset="handleReset" />
    </div>

    <!-- 按钮区域 -->
    <div class="table-header">
      <div class="button-group">
        <el-button type="primary" class="operation-btn" @click="handleExport">导出</el-button>
        <el-button type="primary" class="operation-btn" @click="handleConfigRisk">风险指标配置</el-button>
      </div>
    </div>

    <!-- 表格区域 -->
    <el-table
      :data="tableData"
      style="width: 100%"
      :header-cell-style="headerCellStyle"
      :row-class-name="tableRowClassName"
      @row-click="handleRowClick"
      :max-height="tableMaxHeight"
      empty-text="暂无数据"
      v-loading="loading"
    >
      <el-table-column label="序号" min-width="60">
        <template #default="{ $index }">
          {{ (currentPage - 1) * pageSize + $index + 1 }}
        </template>
      </el-table-column>
      <el-table-column prop="riskCode" label="风险编码" min-width="150" />
      <el-table-column label="压力级别" min-width="100">
        <template #default="scope">
          {{ scope.row.pressureLevelName || '-' }}
        </template>
      </el-table-column>
      <el-table-column prop="pipelineId" label="管线编码" min-width="150" />
      <el-table-column prop="riskLevelName" label="风险等级" min-width="100" />
      <!-- <el-table-column prop="material" label="管材" min-width="100" /> -->
      <el-table-column label="评估日期" min-width="170">
        <template #default="scope">
          {{ formatDate(scope.row.assessmentDate) }}
        </template>
      </el-table-column>
      <el-table-column prop="managementUnitName" label="权属单位" min-width="170" />
      <el-table-column label="风险等级" min-width="120">
        <template #default="scope">
          <div :class="getRiskLevelClass(scope.row.riskLevel)">
            {{ getRiskLevelText(scope.row.riskLevel) }}
          </div>
        </template>
      </el-table-column>
      <el-table-column label="评估日期" min-width="170">
        <template #default="scope">
          {{ formatDate(scope.row.assessmentDate) }}
        </template>
      </el-table-column>
      <el-table-column label="管控状态" min-width="120">
        <template #default="scope">
          {{ CONTROL_STATUS_MAP[scope.row.pipelineStatus] || '-' }}
        </template>
      </el-table-column>
      <el-table-column label="操作" min-width="220" fixed="right" align="center">
        <template #default="scope">
          <div class="operation-btns">
            <el-button type="primary" link @click.stop="handleAssessRecord(scope.row)">评估记录</el-button>
            <el-button type="primary" link @click.stop="handleEdit(scope.row)">修改</el-button>
            <el-button type="primary" link @click.stop="handleLocation(scope.row)">定位</el-button>
          </div>
        </template>
      </el-table-column>
    </el-table>

    <!-- 分页区域 -->
    <div class="pagination-container">
      <el-pagination
        v-model:current-page="currentPage"
        v-model:page-size="pageSize"
        :page-sizes="[10, 20, 30, 50]"
        layout="total, sizes, prev, pager, next, jumper"
        :total="total"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
        :pager-count="5"
      />
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, reactive, nextTick, onUnmounted } from 'vue';
import { ElTable, ElTableColumn, ElButton, ElPagination, ElMessage } from 'element-plus';
import GasRiskSearch from './components/GasRiskSearch.vue';
import { getRiskAssessmentPipelineListPage, getPipelineRiskStatistics } from '@/api/gas';
import { RISK_LEVEL_MAP, CONTROL_STATUS_MAP } from '@/constants/gas';
import moment from 'moment';
import { misPosition } from '@/hooks/gishooks';
// 分页相关
const currentPage = ref(1);
const pageSize = ref(10);
const total = ref(0);
const tableData = ref([]);

// 查询参数
const queryParams = ref({});

// 加载状态
const loading = ref(false);

// 风险统计数据
const riskLevelData = reactive({
  '7001': 0, // 重大风险
  '7002': 0, // 较大风险
  '7003': 0, // 一般风险
  '7004': 0  // 低风险
});

// 处理搜索
const handleSearch = (formData) => {
  queryParams.value = formData;
  currentPage.value = 1;
  fetchRiskData();
};

// 处理重置
const handleReset = () => {
  queryParams.value = {};
  currentPage.value = 1;
  fetchRiskData();
};

// 获取风险数据
const fetchRiskData = async () => {
  loading.value = true;
  try {
    const response = await getRiskAssessmentPipelineListPage({
      ...queryParams.value,
      pageNum: currentPage.value,
      pageSize: pageSize.value
    });
    
    if (response && response.code === 200) {
      tableData.value = response.data.records || [];
      total.value = response.data.total || 0;
      
      // 更新风险统计数据
      updateRiskStats(response.data.stats);
    } else {
      ElMessage.error(response.message || '获取数据失败');
    }
  } catch (error) {
    console.error('获取风险数据失败:', error);
    ElMessage.error('获取数据失败，请稍后重试');
  } finally {
    loading.value = false;
  }
};

// 获取风险统计数据
const fetchRiskStatistics = async () => {
  try {
    const response = await getPipelineRiskStatistics();
    if (response && response.code === 200 && response.data) {
      const riskLevelStats = response.data.riskLevelStatistics || [];
      resetRiskStats();
      
      riskLevelStats.forEach(item => {
        if (item.code && item.length !== undefined) {
          // const lengthInKm = (item.length / 1000).toFixed(2);
          const lengthInKm = item.length;
          riskLevelData[item.code] = lengthInKm;
        }
      });
    }
  } catch (error) {
    console.error('获取风险统计数据失败:', error);
  }
};

// 更新风险统计数据 (保留原有方法，用于表格数据返回的情况)
const updateRiskStats = (stats) => {
  if (stats) {
    riskLevelData['7001'] = stats.criticalCount || 0;
    riskLevelData['7002'] = stats.majorCount || 0;
    riskLevelData['7003'] = stats.normalCount || 0;
    riskLevelData['7004'] = stats.lowCount || 0;
  } else {
    resetRiskStats();
  }
};

// 重置风险统计数据
const resetRiskStats = () => {
  riskLevelData['7001'] = 0;
  riskLevelData['7002'] = 0;
  riskLevelData['7003'] = 0;
  riskLevelData['7004'] = 0;
};

// 处理分页大小变化
const handleSizeChange = (val) => {
  pageSize.value = val;
  currentPage.value = 1;
  fetchRiskData();
};

// 处理页码变化
const handleCurrentChange = (val) => {
  currentPage.value = val;
  fetchRiskData();
};

// 获取风险等级文本
const getRiskLevelText = (level) => {
  return RISK_LEVEL_MAP[level] || '';
};

// 获取风险等级样式
const getRiskLevelClass = (level) => {
  const map = {
    7001: 'risk-level-major',
    7002: 'risk-level-high',
    7003: 'risk-level-normal',
    7004: 'risk-level-low'
  };
  return ['risk-level-tag', map[level]];
};

// 格式化日期
const formatDate = (dateStr) => {
  if (!dateStr) return '';
  return moment(dateStr).format('YYYY-MM-DD');
};

// 表头样式
const headerCellStyle = {
  background: '#F5F7FA',
  color: '#606266',
  height: '40px',
  fontWeight: 'bold'
};

// 斑马纹和选中行样式
const tableRowClassName = ({ rowIndex }) => {
  return rowIndex % 2 === 0 ? 'even-row' : 'odd-row';
};

// 处理行点击
const handleRowClick = (row) => {
  console.log('行数据:', row);
};

// 操作按钮处理函数
const handleExport = () => {
  console.log('导出');
};

const handleConfigRisk = () => {
  console.log('风险指标配置');
};

const handleAssessRecord = (row) => {
  console.log('评估记录:', row);
};

const handleEdit = (row) => {
  console.log('修改:', row);
};

// 处理定位
const handleLocation = (row) => {
  if (
    row.startPointLongitude &&
    row.startPointLongitude != '' &&
    row.startPointLatitude &&
    row.startPointLatitude != ''
  ) {
    misPosition.value = {
      longitude: row.startPointLongitude, //经度
      latitude: row.startPointLatitude //纬度
    }
  } else {
    ElMessage.warning('没有经纬度，无法定位！')
  }
};

const tableMaxHeight = ref(500);

// 计算表格最大高度
const calculateTableMaxHeight = () => {
  nextTick(() => {
    const viewportHeight = window.innerHeight;
    const container = document.querySelector('.gas-risk-explosion-assessment');
    if (!container) {
      setTimeout(calculateTableMaxHeight, 100);
      return;
    }
    const containerRect = container.getBoundingClientRect();
    const containerTop = containerRect.top;

    const riskStatsSection = container.querySelector('.risk-stats-section');
    const riskStatsHeight = riskStatsSection ? riskStatsSection.offsetHeight + 16 : 0;

    const searchSection = container.querySelector('.search-section');
    const searchHeight = searchSection ? searchSection.offsetHeight : 60;

    const headerSection = container.querySelector('.table-header');
    const headerHeight = headerSection ? headerSection.offsetHeight : 60;

    const paginationReservedHeight = 80;
    const bottomReserved = 30;
    const availableHeight = viewportHeight - containerTop - riskStatsHeight - searchHeight - headerHeight - paginationReservedHeight - bottomReserved;
    const minHeight = 300;
    const absoluteMaxHeight = 600;
    const maxHeight = Math.max(Math.min(availableHeight, absoluteMaxHeight), minHeight);
    tableMaxHeight.value = maxHeight;
  });
};

// 窗口大小改变时重新计算表格最大高度
const handleResize = () => {
  clearTimeout(handleResize.timer);
  handleResize.timer = setTimeout(() => {
    calculateTableMaxHeight();
  }, 100);
};

onMounted(() => {
  fetchRiskData();
  fetchRiskStatistics();
  setTimeout(() => {
    calculateTableMaxHeight();
  }, 100);
  window.addEventListener('resize', handleResize);
});

onUnmounted(() => {
  window.removeEventListener('resize', handleResize);
  if (handleResize.timer) {
    clearTimeout(handleResize.timer);
  }
});
</script>

<style scoped>
.gas-risk-explosion-assessment {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  padding: 16px;
  background-color: white;
}

/* 风险统计区域样式 */
.risk-stats-section {
  width: 100%;
  max-width: 1648px;
  height: 132px;
  background: #FFFFFF;
  padding: 16px;
  display: flex;
  justify-content: space-between;
  border-radius: 4px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.05);
  margin-bottom: 16px;
}

.risk-card {
  width: 380px;
  height: 100%;
  border-radius: 4px;
  display: flex;
  flex-direction: column;
  justify-content: center;
  padding: 0 24px;
}

.risk-card:not(:last-child) {
  margin-right: 32px;
}

.major-risk {
  background: linear-gradient(180deg, #F62609 0%, #FF7D52 100%);
}

.high-risk {
  background: linear-gradient(180deg, #F68409 0%, #FFC46A 100%);
}

.normal-risk {
  background: linear-gradient(180deg, #F6CE09 0%, #F7DD5D 100%);
}

.low-risk {
  background: linear-gradient(180deg, #0979F6 0%, #40C3FA 100%);
}

.risk-title {
  font-family: PingFangSC, PingFang SC;
  font-weight: 500;
  font-size: 18px;
  color: #FFFFFF;
  margin-bottom: 8px;
}

.risk-value {
  display: flex;
  align-items: baseline;
}

.value {
  font-family: D-DIN, D-DIN;
  font-weight: bold;
  font-size: 30px;
  color: #FFFFFF;
  margin-right: 4px;
}

.unit {
  font-size: 14px;
  color: #FFFFFF;
}

/* 搜索表单区域 */
.search-section {
  margin-bottom: 16px;
}

/* 表格头部样式 */
.table-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.button-group {
  display: flex;
  gap: 8px;
}

.operation-btn {
  height: 32px;
  padding: 0 16px;
  background: #0277FD;
  border-radius: 2px;
}

/* 表格样式 */
:deep(.el-table) {
  --el-table-border-color: #EBEEF5;
  --el-table-header-bg-color: #F5F7FA;
}

:deep(.el-table__body-wrapper) {
  overflow: auto !important;
  scrollbar-width: thin;
  scrollbar-color: #c1c1c1 transparent;
}

:deep(.el-table__body-wrapper::-webkit-scrollbar) {
  height: 8px;
  width: 8px;
}

:deep(.el-table__body-wrapper::-webkit-scrollbar-track) {
  background: #f5f5f5;
  border-radius: 4px;
}

:deep(.el-table__body-wrapper::-webkit-scrollbar-thumb) {
  background-color: #c0c4cc;
  border-radius: 4px;
}

:deep(.el-table__body-wrapper::-webkit-scrollbar-thumb:hover) {
  background-color: #909399;
}

:deep(.el-table .even-row) {
  background-color: #FAFAFA;
}

:deep(.el-table .odd-row) {
  background-color: #FFFFFF;
}

/* 风险等级标签样式 */
.risk-level-tag {
  width: 72px;
  height: 32px;
  border-radius: 4px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-family: PingFangSC, PingFang SC;
  font-weight: 400;
  font-size: 14px;
}

.risk-level-major {
  background: rgba(255,0,0,0.1);
  border: 1px solid #FF0000;
  color: #FF0000;
}

.risk-level-high {
  background: rgba(255,133,0,0.1);
  border: 1px solid #FF8500;
  color: #FF8500;
}

.risk-level-normal {
  background: rgba(255,211,0,0.1);
  border: 1px solid #FFD300;
  color: #FFD300;
}

.risk-level-low {
  background: rgba(0,122,255,0.1);
  border: 1px solid #007AFF;
  color: #007AFF;
}

.operation-btns {
  display: flex;
  gap: 8px;
  justify-content: center;
}

/* 分页样式 */
.pagination-container {
  margin-top: 16px;
  display: flex;
  justify-content: flex-end;
}

/* 响应式适配 */
@media screen and (max-width: 1920px) {
  .risk-stats-section {
    width: 100%;
  }
  
  .risk-card {
    width: calc(25% - 24px);
  }
}
</style>