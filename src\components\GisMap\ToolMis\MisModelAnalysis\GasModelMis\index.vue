<template>
    <div class="component-model-list">
        <div class="model_level2"
             :class="[state.selectedTitle === 2 ? 'select_model_2': '']"
             v-if="state.selectedTitle === 2"
        >
            <div
                    v-for="item in model_l2Data[2]"
                    :key="item.title"
                    class="butt-normal"
                    :class="[state.selectedLevel2 === item.id ? 'butt-active' : '']"
                    @click="handleClickLevel2(item.id)"
            >
                {{ item.title }}
            </div>
        </div>
        <div class="model_level2"
             :class="[state.selectedTitle === 3 ? 'select_model_3': '']"
             v-if="state.selectedTitle === 3"
        >
            <div
                    v-for="item in model_l2Data[3]"
                    :key="item.title"
                    class="butt-normal"
                    :class="[state.selectedLevel2 === item.id ? 'butt-active' : '']"
                    @click="handleClickLevel2(item.id)"
            >
                {{ item.title }}
            </div>
        </div>
        <LeakageAnalysis
                v-if="state.selectedTitle === 1 && state.showResult"
                :data="state.data"
                @close="handleClose"
        />
        <OpenSpaceDiffusionAnalysis
                v-if="state.selectedTitle === 2 &&
            state.selectedLevel2 === 4 &&
            state.showOpenSpaceType === 0 &&
            state.showResult"
                :data="state.data"
                @close="handleClose"
        />
        <UndergroundSpaceDiffusionAnalysis
                v-if="state.selectedTitle === 2 &&
            state.selectedLevel2 === 5 &&
            state.showResult"
                :data="state.data"
                @close="handleClose"
        />
        <OpenSpaceExplosionAnalysis
                v-if="state.selectedTitle === 3 &&
            state.selectedLevel2 === 6 &&
            state.showOpenSpaceType === 0 &&
            state.showResult"
                :data="state.data"
                @close="handleClose"
        />
        <UndergroundSpaceExplosionAnalysis
                v-if="state.selectedTitle === 3 &&
            state.selectedLevel2 === 7 &&
            state.showResult"
                :data="state.data"
                @close="handleClose"
        />
        <OpenSpaceDiffusionModel
                v-if="state.selectedTitle === 2 &&
            state.selectedLevel2 === 4 &&
            state.showOpenSpaceType === 4"
                @analyze="handleAnalyze"
                @close="handleClose"
        />
        <OpenSpaceExplosionModel
                v-if="state.selectedTitle === 3 &&
            state.selectedLevel2 === 6 &&
            state.showOpenSpaceType === 6"
                @analyze="handleAnalyze"
                @close="handleClose"
        />
        <ExplosionLegend
                v-if="state.selectedTitle === 3 &&
            state.selectedLevel2 === 6 &&
            state.showOpenSpaceType === 0 &&
            state.showResult"
        />
    </div>
</template>

<script setup>
import bus from "@/utils/mitt.js";
import {onBeforeUnmount, onUnmounted, reactive, watch} from "vue";
import OpenSpaceDiffusionModel from "./components/OpenSpaceDiffusionModel.vue";
import OpenSpaceExplosionModel from "./components/OpenSpaceExplosionModel.vue";
import LeakageAnalysis from "./components/LeakageAnalysis.vue";
import OpenSpaceDiffusionAnalysis from "./components/OpenSpaceDiffusionAnalysis.vue";
import UndergroundSpaceDiffusionAnalysis from "./components/UndergroundSpaceDiffusionAnalysis.vue";
import OpenSpaceExplosionAnalysis from "./components/OpenSpaceExplosionAnalysis.vue";
import UndergroundSpaceExplosionAnalysis from "./components/UndergroundSpaceExplosionAnalysis.vue";
import ExplosionLegend from "./components/ExplosionLegend.vue";
import {useRoute} from "vue-router";
import {mapStates} from "@/components/GisMap/mapStates.js";

const route = useRoute();

const state = reactive({
    data: {},
    selectedTitle: 0,
    selectedLevel2: 4,
    showOpenSpaceType: 0,
    showResult: false,
});
const model_l2Data = reactive({
    2: [{
        id: 4,
        title: "开放空间泄漏扩散分析",
    },
        {
            id: 5,
            title: "地下空间泄漏扩散分析",
        }],
    3: [{
        id: 6,
        title: "开放空间致灾后果预测",
    },
        {
            id: 7,
            title: "地下空间爆炸后果预测",
        }]
});

const handleClickLevel2 = (e) => {
    //todo 激活模型
    state.selectedLevel2 = e;
    state.showResult = false;
    drawCircleBuffers();
};

const handleClose = () => {
    state.showResult = false;
    state.showOpenSpaceType = 0;
    //todo 清除模型分析结果
    drawCircleBuffers();
};

const handleAnalyze = (formData) => {
    //todo 模型分析 formData
    state.showOpenSpaceType = 0; // 设置为开放空间类型
    state.showResult = true;
    drawCircleBuffers();
};

bus.on("misModelAnalysis", (data) => {
    state.data = data;
    state.showOpenSpaceType = [2, 3].includes(state.selectedTitle) &&
    [4, 6].includes(state.selectedLevel2) ? state.selectedLevel2 : 0;
    state.showResult = state.showOpenSpaceType === 0;
    drawCircleBuffers();
})

watch(() => route.path,
    (newPath) => {
        //todo 激活模型
        state.selectedTitle = newPath === '/gas/predict/warning/source' ? 1 :
            newPath === '/gas/predict/warning/diffusion' ? 2 :
            newPath === '/gas/predict/warning/damage' ? 3 : 0;
        if (state.selectedTitle === 2) {
            state.selectedLevel2 = 4;
        } else if (state.selectedTitle === 3) {
            state.selectedLevel2 = 6;
        }
        state.showResult = false;
        mapStates.earth.entity.clearLayersByIds(["gasCircleBuffer", "gasCircleBuffer1", "gasCircleBuffer2"]);
    },
    {
        immediate: true,
        deep: true
    }
)

const drawCircleBuffers = () => {
    mapStates.earth.entity.clearLayersByIds(["gasCircleBuffer", "gasCircleBuffer1", "gasCircleBuffer2"]);
    if (state.selectedTitle === 2 &&
        state.selectedLevel2 === 4 &&
        state.showOpenSpaceType === 0 &&
        state.showResult) {
        // 开放空间泄漏扩散分析
        mapStates.earth.entity.addCircleGeometryFromDegrees({
            layerId: "gasCircleBuffer",
            position: [state.data?.longitude, state.data?.latitude],
            radius: 200,
            colorStr: "#FF2E01",
        });
    } else if (state.selectedTitle === 2 &&
        state.selectedLevel2 === 5 &&
        state.showResult) {
        // 地下空间泄漏扩散分析
        mapStates.earth.entity.addCircleGeometryFromDegrees({
            layerId: "gasCircleBuffer",
            position: [state.data?.longitude, state.data?.latitude],
            radius: 200,
            colorStr: "#FF2E01",
        });
    } else if (state.selectedTitle === 3 &&
        state.selectedLevel2 === 6 &&
        state.showOpenSpaceType === 0 &&
        state.showResult) {
        // 开放空间致灾后果预测

        mapStates.earth.entity.addCircleGeometryFromDegrees({
            layerId: "gasCircleBuffer",
            position: [state.data?.longitude, state.data?.latitude],
            radius: 200,
            colorStr: "#1775f9",
        });
        mapStates.earth.entity.addCircleGeometryFromDegrees({
            layerId: "gasCircleBuffer1",
            position: [state.data?.longitude, state.data?.latitude],
            radius: 100,
            colorStr: "#FF8200",
        });
        mapStates.earth.entity.addCircleGeometryFromDegrees({
            layerId: "gasCircleBuffer2",
            position: [state.data?.longitude, state.data?.latitude],
            radius: 50,
            colorStr: "#FF2E01",
        });
    } else if (state.selectedTitle === 3 &&
        state.selectedLevel2 === 7 &&
        state.showResult) {
        // 地下空间爆炸后果预测
        mapStates.earth.entity.addCircleGeometryFromDegrees({
            layerId: "gasCircleBuffer",
            position: [state.data?.longitude, state.data?.latitude],
            radius: 200,
            colorStr: "#FF2E01",
        });
    }

    if (state.showResult && state.selectedTitle !== 1) {
        window.setTimeout(() => {
            mapStates.earth.entity.focusLayerByLayerId("gasCircleBuffer",{
                height: 1000,
            })
        }, 200);
    }
};

onBeforeUnmount(() => {
    //todo 清理模型分析结果
    mapStates.earth.entity.clearLayersByIds(["gasCircleBuffer", "gasCircleBuffer1", "gasCircleBuffer2"]);
});
</script>

<style lang="scss" scoped>
.component-model-list {
  pointer-events: all;
  width: 178px;

  .model_level2 {
    width: 178px;
  }

  .butt-normal {
    margin-bottom: 10px;
    padding: 8px 10px;
    border-radius: 4px;
    cursor: pointer;
    background: rgba(255, 255, 255, 0.9);
    border: 1px solid rgba(255, 255, 255, 0.9);
    font-size: 14px;
    font-weight: 400;
    color: rgba(0, 0, 0, 0.7);
    text-align: center;

    &:hover {
      background: rgba(26, 142, 231, 0.9);
      border: 1px solid rgba(26, 142, 231, 1);
    }
  }

  .butt-active {
    background: rgba(26, 142, 231, 0.9);
    border: 1px solid rgba(26, 142, 231, 1);
    color: #ffffff;
  }

}
</style>
