<template>
  <div>
    <!-- 管点信息弹窗 -->
    <el-dialog
      :title="dialogTitle"
      v-model="visible"
      width="800px"
      :close-on-click-modal="false"
      @close="handleClose"
    >
      <el-form
        ref="form"
        :model="formData"
        :rules="rules"
        label-width="120px"
        class="dialog-form"
      >
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="管点编码" prop="pointCode">
              <el-input
                v-model="formData.pointCode"
                placeholder="请输入管点编码"
                :disabled="isDetail"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="管点类型" prop="pointType">
              <el-select
                v-model="formData.pointType"
                placeholder="请选择管点类型"
                style="width: 100%"
                :disabled="isDetail"
                @change="handlePointTypeChange"
              >
                <el-option
                  v-for="item in pointTypeOptions"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="埋深(m)" prop="depth">
              <el-input
                v-model="formData.depth"
                placeholder="请输入埋深"
                :disabled="isDetail"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="高程(m)" prop="elevation">
              <el-input
                v-model="formData.elevation"
                placeholder="请输入高程"
                :disabled="isDetail"
              />
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="所在道路" prop="roadName">
              <el-input
                v-model="formData.roadName"
                placeholder="请输入所在道路"
                :disabled="isDetail"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="详细地址" prop="address">
              <el-input
                v-model="formData.address"
                placeholder="请输入详细地址"
                :disabled="isDetail"
              />
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :span="24">
            <el-form-item label="经纬度">
              <div class="flex items-center">
                <el-input v-model="formData.longitude" placeholder="经度" class="mr-2 w-32" :disabled="isDetail" />
                <el-input v-model="formData.latitude" placeholder="纬度" class="w-32" :disabled="isDetail" />
                <el-button type="primary" class="ml-2" icon="Location" circle @click="openMapPicker" :disabled="isDetail"></el-button>
              </div>
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="关联管线" prop="connectedPipeline">
              <el-input
                v-model="formData.connectedPipelineId"
                placeholder="请选择关联管线"
                readonly
                :disabled="isDetail"
              >
                <template #append>
                  <el-button
                    :icon="Connection"
                    @click="openPipelineDialog"
                    :disabled="isDetail"
                    title="选择关联管线"
                  />
                </template>
              </el-input>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="关联窖井" prop="connectedWell">
              <el-input
                v-model="formData.connectedWellId"
                placeholder="请选择关联窖井"
                readonly
                :disabled="isDetail"
              >
                <template #append>
                  <el-button
                    :icon="Place"
                    @click="openWellDialog"
                    :disabled="isDetail"
                    title="选择关联窖井"
                  />
                </template>
              </el-input>
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="权属单位" prop="managementUnit">
              <el-select
                v-model="formData.managementUnit"
                placeholder="请选择权属单位"
                style="width: 100%"
                :disabled="isDetail"
                filterable
                @change="handleManagementUnitChange"
              >
                <el-option
                  v-for="item in enterpriseList"
                  :key="item.id"
                  :label="item.enterpriseName"
                  :value="item.id"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="使用状态" prop="usageStatus">
              <el-select
                v-model="formData.usageStatus"
                placeholder="请选择使用状态"
                style="width: 100%"
                :disabled="isDetail"
                @change="handleUsageStatusChange"
              >
                <el-option
                  v-for="item in useStatusOptions"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :span="24">
            <el-form-item label="所属区域" prop="areaCode">
              <el-cascader
                v-model="areaCodeArray"
                :options="areaOptions"
                :props="{ value: 'code', label: 'name', children: 'children' }"
                placeholder="请选择所属区域"
                style="width: 100%"
                :disabled="isDetail"
                @change="handleAreaChange"
                clearable
              />
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="安装时间" prop="installTime">
              <el-date-picker
                v-model="formData.installTime"
                type="datetime"
                placeholder="请选择安装时间"
                style="width: 100%"
                :disabled="isDetail"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <!-- 空列，保持布局 -->
          </el-col>
        </el-row>



        <el-row>
          <el-col :span="24">
            <el-form-item label="备注" prop="remark">
              <el-input
                v-model="formData.remark"
                type="textarea"
                :rows="3"
                placeholder="请输入备注"
                :disabled="isDetail"
              />
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>

      <template #footer>
        <div class="dialog-footer">
          <el-button @click="handleClose">取消</el-button>
          <el-button v-if="!isDetail" type="primary" @click="handleSubmit">确定</el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 关联管线选择弹窗 -->
    <el-dialog
      title="选择关联管线"
      v-model="pipelineDialogVisible"
      width="800px"
      :close-on-click-modal="false"
    >
      <div class="search-container">
        <el-form :inline="true" class="search-form">
          <el-form-item label="管线编码">
            <el-input
              v-model="pipelineSearchForm.pipelineCode"
              placeholder="请输入管线编码"
              style="width: 200px"
              @keyup.enter="searchPipelines"
            />
          </el-form-item>
          <el-form-item>
            <el-button type="primary" @click="searchPipelines">查询</el-button>
            <el-button @click="resetPipelineSearch">重置</el-button>
          </el-form-item>
        </el-form>
      </div>

      <el-table
        :data="pipelineList"
        style="width: 100%"
        height="400px"
        @row-click="handlePipelineRowClick"
      >
        <el-table-column width="50">
          <template #default="scope">
            <el-radio
              v-model="selectedPipeline"
              :value="scope.row.id"
              @change="handlePipelineSelect(scope.row)"
            > </el-radio>
          </template>
        </el-table-column>
        <el-table-column prop="pipelineCode" label="管线编码" />
      </el-table>

      <el-pagination
        @size-change="handlePipelineSizeChange"
        @current-change="handlePipelineCurrentChange"
        :current-page="pipelinePagination.pageNum"
        :page-sizes="[10, 20, 50, 100]"
        :page-size="pipelinePagination.pageSize"
        layout="total, sizes, prev, pager, next, jumper"
        :total="pipelinePagination.total"
        style="margin-top: 20px; text-align: right"
      />

      <template #footer>
        <div class="dialog-footer">
          <el-button @click="pipelineDialogVisible = false">取消</el-button>
          <el-button type="primary" @click="confirmPipelineSelection">确定</el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 关联窖井选择弹窗 -->
    <el-dialog
      title="选择关联窖井"
      v-model="wellDialogVisible"
      width="800px"
      :close-on-click-modal="false"
    >
      <div class="search-container">
        <el-form :inline="true" class="search-form">
          <el-form-item label="窖井编码">
            <el-input
              v-model="wellSearchForm.wellCode"
              placeholder="请输入窖井编码"
              style="width: 200px"
              @keyup.enter="searchWells"
            />
          </el-form-item>
          <el-form-item>
            <el-button type="primary" @click="searchWells">查询</el-button>
            <el-button @click="resetWellSearch">重置</el-button>
          </el-form-item>
        </el-form>
      </div>

      <el-table
        :data="wellList"
        style="width: 100%"
        height="400px"
        @row-click="handleWellRowClick"
      >
        <el-table-column width="50">
          <template #default="scope">
            <el-radio
              v-model="selectedWell"
              :value="scope.row.id"
              @change="handleWellSelect(scope.row)"
            > </el-radio>
          </template>
        </el-table-column>
        <el-table-column prop="wellCode" label="窖井编码" />
      </el-table>

      <el-pagination
        @size-change="handleWellSizeChange"
        @current-change="handleWellCurrentChange"
        :current-page="wellPagination.pageNum"
        :page-sizes="[10, 20, 50, 100]"
        :page-size="wellPagination.pageSize"
        layout="total, sizes, prev, pager, next, jumper"
        :total="wellPagination.total"
        style="margin-top: 20px; text-align: right"
      />

      <template #footer>
        <div class="dialog-footer">
          <el-button @click="wellDialogVisible = false">取消</el-button>
          <el-button type="primary" @click="confirmWellSelection">确定</el-button>
        </div>
      </template>
    </el-dialog>


  </div>
</template>

<script setup>
import { ref, reactive, computed, onMounted, onUnmounted, nextTick } from 'vue'
import { ElMessage } from 'element-plus'
import { savePoint, updatePoint, getEnterpriseList, getPipelinePage, getManholeWellPage } from '@/api/drainage'
import { POINT_TYPE_OPTIONS, USE_TYPE } from '@/constants/drainage'
import { AREA_OPTIONS } from '@/constants/gas'
import { collectShow } from "@/hooks/gishooks"
import bus from '@/utils/mitt'
import { Connection,Place } from '@element-plus/icons-vue'
import moment from 'moment'

// 定义 emits
const emit = defineEmits(['success'])

// 响应式数据
const visible = ref(false)
const dialogType = ref('add') // add, edit, detail
const form = ref(null)

const formData = reactive({
  pointCode: '',
  pointType: '',
  pointTypeName: '',
  depth: '',
  elevation: '',
  roadName: '',
  address: '',
  connectedPipeline: '',
  connectedPipelineId: '',
  connectedWell: '',
  connectedWellId: '',
  managementUnit:'',
  managementUnitName: '',
  usageStatus: '',
  usageStatusName: '',
  longitude: '',
  latitude: '',
  installTime: '',
  county: '',
  countyName: '',
  town: '',
  townName: '',
  remark: ''
})

const rules = {
  pointCode: [
    { required: true, message: '请输入管点编码', trigger: 'blur' }
  ],
  pointType: [
    { required: true, message: '请选择管点类型', trigger: 'change' }
  ]
}

const pointTypeOptions = POINT_TYPE_OPTIONS.filter(item => item.value !== '')
const useStatusOptions = USE_TYPE
const enterpriseList = ref([])
const areaCodeArray = ref([])
const areaOptions = AREA_OPTIONS

// 地图选点相关
const currentPointType = ref('')

// 关联管线弹窗相关
const pipelineDialogVisible = ref(false)
const pipelineList = ref([])
const pipelineSearchForm = reactive({
  pipelineCode: ''
})
const pipelinePagination = reactive({
  pageNum: 1,
  pageSize: 10,
  total: 0
})
const selectedPipeline = ref('')
const selectedPipelineData = ref(null)

// 关联窖井弹窗相关
const wellDialogVisible = ref(false)
const wellList = ref([])
const wellSearchForm = reactive({
  wellCode: ''
})
const wellPagination = reactive({
  pageNum: 1,
  pageSize: 10,
  total: 0
})
const selectedWell = ref('')
const selectedWellData = ref(null)

// 计算属性
const dialogTitle = computed(() => {
  const titleMap = {
    add: '新增管点信息',
    edit: '编辑管点信息',
    detail: '管点详情'
  }
  return titleMap[dialogType.value] || '管点信息'
})

const isDetail = computed(() => {
  return dialogType.value === 'detail'
})
const handleManagementUnitChange = (value) => {
  // 根据选中的value找到对应的label
  const selectedOption = enterpriseList.value.find(item => item.id === value)
  if (selectedOption) {
    formData.managementUnit = selectedOption.id
    formData.managementUnitName = selectedOption.enterpriseName
  }
}

const handlePointTypeChange = (value) => {
  // 根据选中的value找到对应的label
  const selectedOption = pointTypeOptions.find(item => item.value === value)
  if (selectedOption) {
    formData.pointType = selectedOption.value
    formData.pointTypeName = selectedOption.label
  }
}

const handleUsageStatusChange = (value) => {
  // 根据选中的value找到对应的label
  const selectedOption = useStatusOptions.find(item => item.value === value)
  if (selectedOption) {
    formData.usageStatus = selectedOption.value
    formData.usageStatusName = selectedOption.label
  }
}

const handleAreaChange = (value) => {
  if (value && value.length >= 2) {
    // 县级固定为"371728"（东明县）
    formData.county = '371728'
    formData.countyName = '东明县'
    
    // 乡镇为选择的第二级值
    formData.town = value[1]
    
    // 查找乡镇名称
    const countyOption = areaOptions.find(item => item.code === '371728')
    if (countyOption && countyOption.children) {
      const townOption = countyOption.children.find(item => item.code === value[1])
      if (townOption) {
        formData.townName = townOption.name
      }
    }
  } else {
    // 清空选择
    formData.county = ''
    formData.countyName = ''
    formData.town = ''
    formData.townName = ''
  }
}
// 方法
const open = (type, data = {}) => {
  dialogType.value = type
  visible.value = true
  
  if (type === 'add') {
    resetForm()
  } else {
    Object.assign(formData, data)
    // 处理关联数据显示
    if (data.connectedPipeline) {
      formData.connectedPipelineId = data.connectedPipelineId || ''
    }
    if (data.connectedWell) {
      formData.connectedWellId = data.connectedWellId || ''
    }
    
    // 处理管点类型反显：如果有pointType但没有pointTypeName，则根据pointType查找对应的label
    if (data.pointType && !data.pointTypeName) {
      const selectedOption = pointTypeOptions.find(item => item.value === data.pointType)
      if (selectedOption) {
        formData.pointTypeName = selectedOption.label
      }
    }
    
    // 处理使用状态反显：如果有usageStatus但没有usageStatusName，则根据usageStatus查找对应的label
    if (data.usageStatus && !data.usageStatusName) {
      const selectedOption = useStatusOptions.find(item => item.value === data.usageStatus)
      if (selectedOption) {
        formData.usageStatusName = selectedOption.label
      }
    }
    
    // 处理权属单位反显：如果有managementUnit但没有managementUnitName，则根据managementUnit查找对应的label
    if (data.managementUnit && !data.managementUnitName) {
      const selectedOption = enterpriseList.value.find(item => item.id === data.managementUnit)
      if (selectedOption) {
        formData.managementUnitName = selectedOption.enterpriseName
      }
    }
    
    // 处理区域反显：根据county和town设置级联选择器的值
    if (data.county && data.town) {
      areaCodeArray.value = [data.county, data.town]
    } else if (data.county) {
      areaCodeArray.value = [data.county]
    } else {
      areaCodeArray.value = []
    }
    
    // 处理县区名称反显：如果有county但没有countyName，则设置为东明县
    if (data.county === '371728' && !data.countyName) {
      formData.countyName = '东明县'
    }
    
    // 处理乡镇名称反显：如果有town但没有townName，则根据town查找对应的name
    if (data.town && !data.townName) {
      const countyOption = areaOptions.find(item => item.code === '371728')
      if (countyOption && countyOption.children) {
        const townOption = countyOption.children.find(item => item.code === data.town)
        if (townOption) {
          formData.townName = townOption.name
        }
      }
    }
  }
  
  loadEnterpriseList()
}

// 重置表单
const resetForm = () => {
  Object.assign(formData, {
    pointCode: '',
    pointType: '',
    pointTypeName: '',
    depth: '',
    elevation: '',
    roadName: '',
    address: '',
    connectedPipeline: '',
    connectedPipelineId: '',
    connectedWell: '',
    connectedWellId: '',
    managementUnit:'',
    managementUnitName: '',
    usageStatus: '',
    usageStatusName: '',
    longitude: '',
    latitude: '',
    installTime: '',
    county: '',
    countyName: '',
    town: '',
    townName: '',
    remark: ''
  })
  areaCodeArray.value = []
  if (form.value) {
    form.value.clearValidate()
  }
}

// 关闭弹窗
const handleClose = () => {
  visible.value = false
  resetForm()
}

// 提交表单
const handleSubmit = () => {
  form.value.validate(async (valid) => {
    if (valid) {
      try {
        const submitData = { ...formData }
        
        // 处理时间格式
        if (submitData.installTime) {
          submitData.installTime = moment(submitData.installTime).format('YYYY-MM-DD HH:mm:ss')
        }
        
        if (dialogType.value === 'add') {
          await savePoint(submitData)
          ElMessage.success('新增成功')
        } else if (dialogType.value === 'edit') {
          await updatePoint(submitData)
          ElMessage.success('更新成功')
        }
        
        handleClose()
        emit('success')
      } catch (error) {
        console.error('提交失败:', error)
        ElMessage.error('操作失败，请重试')
      }
    }
  })
}

// 加载权属单位列表
const loadEnterpriseList = async () => {
  try {
    const response = await getEnterpriseList()
    enterpriseList.value = response.data || []
  } catch (error) {
    console.error('加载权属单位失败:', error)
  }
}



// 地图选点事件处理函数
const handleCollectLocation = (params) => {
  nextTick(() => {
    formData.longitude = params.longitude
    formData.latitude = params.latitude
  })
}

// 打开地图选点
const openMapPicker = () => {
  collectShow.value = true // 激活采集点位窗口
  // 先移除可能存在的旧监听器
  bus.off("getCollectLocation", handleCollectLocation)
  // 添加新的监听器
  bus.on("getCollectLocation", handleCollectLocation)
}

// 打开管线选择弹窗
const openPipelineDialog = () => {
  pipelineDialogVisible.value = true
  selectedPipeline.value = ''
  selectedPipelineData.value = null
  loadPipelineList()
}

// 加载管线列表
const loadPipelineList = async () => {
  try {
    const params = {
      pageNum: pipelinePagination.pageNum,
      pageSize: pipelinePagination.pageSize,
      ...pipelineSearchForm
    }
    const response = await getPipelinePage(params)
    pipelineList.value = response.data.records || []
    pipelinePagination.total = response.data.total || 0
  } catch (error) {
    console.error('加载管线列表失败:', error)
  }
}

// 搜索管线
const searchPipelines = () => {
  pipelinePagination.pageNum = 1
  loadPipelineList()
}

// 重置管线搜索
const resetPipelineSearch = () => {
  Object.assign(pipelineSearchForm, { pipelineCode: '' })
  searchPipelines()
}

// 管线分页大小改变
const handlePipelineSizeChange = (val) => {
  pipelinePagination.pageSize = val
  pipelinePagination.pageNum = 1
  loadPipelineList()
}

// 管线当前页改变
const handlePipelineCurrentChange = (val) => {
  pipelinePagination.pageNum = val
  loadPipelineList()
}

// 管线行点击
const handlePipelineRowClick = (row) => {
  selectedPipeline.value = row.id
  selectedPipelineData.value = row
}

// 管线选择
const handlePipelineSelect = (row) => {
  selectedPipelineData.value = row
}

// 确认管线选择
const confirmPipelineSelection = () => {
  if (selectedPipelineData.value) {
    formData.connectedPipeline = selectedPipelineData.value.id
    formData.connectedPipelineId = selectedPipelineData.value.pipelineCode
    pipelineDialogVisible.value = false
  } else {
    ElMessage.warning('请选择一条管线')
  }
}

// 打开窖井选择弹窗
const openWellDialog = () => {
  wellDialogVisible.value = true
  selectedWell.value = ''
  selectedWellData.value = null
  loadWellList()
}

// 加载窖井列表
const loadWellList = async () => {
  try {
    const params = {
      pageNum: wellPagination.pageNum,
      pageSize: wellPagination.pageSize,
      ...wellSearchForm
    }
    const response = await getManholeWellPage(params)
    wellList.value = response.data.records || []
    wellPagination.total = response.data.total || 0
  } catch (error) {
    console.error('加载窖井列表失败:', error)
  }
}

// 搜索窖井
const searchWells = () => {
  wellPagination.pageNum = 1
  loadWellList()
}

// 重置窖井搜索
const resetWellSearch = () => {
  Object.assign(wellSearchForm, { wellCode: '' })
  searchWells()
}

// 窖井分页大小改变
const handleWellSizeChange = (val) => {
  wellPagination.pageSize = val
  wellPagination.pageNum = 1
  loadWellList()
}

// 窖井当前页改变
const handleWellCurrentChange = (val) => {
  wellPagination.pageNum = val
  loadWellList()
}

// 窖井行点击
const handleWellRowClick = (row) => {
  selectedWell.value = row.id
  selectedWellData.value = row
}

// 窖井选择
const handleWellSelect = (row) => {
  selectedWellData.value = row
}

// 确认窖井选择
const confirmWellSelection = () => {
  if (selectedWellData.value) {
    formData.connectedWell = selectedWellData.value.id
    formData.connectedWellId = selectedWellData.value.wellCode
    wellDialogVisible.value = false
  } else {
    ElMessage.warning('请选择一个窖井')
  }
}

// 暴露方法给父组件
defineExpose({
  open
})

// 组件卸载时清理事件监听
onUnmounted(() => {
  bus.off("getCollectLocation", handleCollectLocation)
})

// 生命周期
onMounted(() => {
  loadEnterpriseList()
})
</script>

<style scoped>
.dialog-form {
  padding: 0 20px;
}

.search-container {
  margin-bottom: 20px;
}

.search-form {
  margin: 0;
}

.dialog-footer {
  text-align: right;
}

.el-table {
  margin-bottom: 20px;
}

.el-radio {
  margin-right: 0;
}
</style>