# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Development Commands

### Core Development
```bash
# Start development server
npm run dev

# Build for production
npm run build

# Build for development environment
npm run build:dev

# Preview production build
npm run preview
```

### Architecture Overview

This is a Vue 3 city lifeline safety monitoring platform (城市生命线安全监管平台) with dual interfaces:

**Screen Interface** (`/screen/*`): Large-screen data visualization dashboard with immersive map-overlay design
**Admin Interface** (`/admin/*`): Traditional management interface with 4-level deep nested routing

### Key Technologies
- **Vue 3** with Composition API and `<script setup>`
- **Element Plus** for UI components
- **Pinia** for state management
- **Cesium** for 3D GIS mapping
- **ECharts 5.6** with GL extension for data visualization
- **Vite** for build tooling
- **Tailwind CSS** for utility-first styling

### Domain Structure
The application is organized around city lifeline domains:
- **Comprehensive** (综合专项): Cross-domain coordination and emergency management
- **Gas** (燃气专项): Gas pipeline monitoring, leak detection, and explosion risk assessment
- **Drainage** (排水专项): Drainage system, flood control, and water quality monitoring
- **Heating** (供热专项): District heating system monitoring and risk management
- **Bridge** (桥梁专项): Bridge structural health monitoring and safety assessment

### Component Architecture

**Screen Components** (`src/components/screen/`):
- `panels/`: Domain-specific panel components for each lifeline domain
- `charts/`: Reusable chart components with auto-resize
- `common/`: Shared components (ScrollTable, VideoPlayer, StatCard)
- `ChartBox.vue`: ECharts wrapper with responsive resizing
- `PanelBox.vue`: Standard panel container with consistent styling
- `StatCard.vue`: Statistics display cards

**GIS Components** (`src/components/GisMap/`):
- `InitMap.vue`: Cesium 3D map initialization with performance optimizations
- `components/`: Map-specific UI components and controls
- `popup/`: Interactive map popup components for device details
- `Tools/`: Map interaction and analysis tools (gas diffusion, drainage simulation)

### State Management (Pinia)
- `user.js`: Authentication with RSA encryption and token management
- `bridge.js`: Bridge-specific monitoring state and device selection
- `alarm.js`: Global alarm system with real-time notifications
- Uses Composition API pattern with reactive state

### API Organization
Modular API structure in `src/api/`:
- `gas.js`, `drainage.js`, `heating.js`, `bridge.js`: Domain-specific APIs
- `comprehensive.js`: Cross-domain coordination APIs
- `user.js`: Authentication with RSA encryption
- `base.js`: Common functionality like video streaming
- Unified request interceptor with automatic token injection and error handling

### Routing System
- **Dynamic routing**: `/:primaryTab/:secondaryTab` pattern for domain navigation
- **Admin routes**: 4-level deep nested structure for management functions
- **Authentication guards**: Token-based route protection
- Bridge domain uses custom routing without secondary navigation

### Screen Adaptation
Advanced responsive design supporting multiple display ratios:
- **16:9 screens**: 380px side panels
- **21:9 ultra-wide**: 420px side panels  
- **32:9 dual-wide**: 450px side panels
- Uses CSS Grid with `minmax(0, 1fr)` for adaptive center content
- Media queries for automatic layout adjustment

### Table Component Optimization Patterns
When optimizing table components, follow this established pattern:

1. **Container Structure**:
   ```vue
   <!-- Main container with flex layout -->
   <div class="component-name">
     <!-- Search section with flex-shrink: 0 -->
     <div class="search-section">
       <SearchComponent />
     </div>
     
     <!-- Table container with flex: 1 -->
     <div class="table-container" ref="tableContainerRef">
       <el-table :height="tableHeight" />
     </div>
     
     <!-- Pagination with flex-shrink: 0 -->
     <div class="pagination-container">
       <el-pagination />
     </div>
   </div>
   ```

2. **CSS Pattern**:
   ```css
   .component-name {
     width: 100%;
     height: 100%;
     display: flex;
     flex-direction: column;
     padding: 8px; /* Reduced from 16px for more space */
     box-sizing: border-box;
     overflow: hidden;
   }
   
   .table-container {
     flex: 1;
     min-height: 0;
     overflow: hidden;
     display: flex;
     flex-direction: column;
   }
   
   :deep(.el-table) {
     height: 100% !important;
     display: flex;
     flex-direction: column;
   }
   
   :deep(.el-table__header-wrapper) {
     flex-shrink: 0;
     overflow-x: hidden !important; /* Hide header scrollbar */
   }
   
   :deep(.el-table__body-wrapper) {
     flex: 1;
     overflow: auto !important;
   }
   ```

3. **JavaScript Pattern**:
   ```javascript
   // Table height calculation
   const tableContainerRef = ref(null);
   const tableHeight = ref(400);
   
   const calculateTableHeight = () => {
     nextTick(() => {
       if (tableContainerRef.value) {
         const containerHeight = tableContainerRef.value.offsetHeight;
         if (containerHeight > 0) {
           tableHeight.value = Math.max(containerHeight, 500);
         } else {
           tableHeight.value = 600;
           setTimeout(calculateTableHeight, 100);
         }
       }
     });
   };
   
   // Scroll synchronization for horizontal scrolling
   const setupScrollSync = () => {
     // Implementation for syncing header and body scroll
   };
   ```

### Environment Configuration
Uses environment-specific configuration:
- `.env.development`: Development API endpoints and proxy settings
- `.env.production`: Production settings and deployment configuration
- Proxy configuration in `vite.config.js` for API forwarding with timeout handling

### Authentication System
- **RSA encryption**: Client-side password encryption with JSEncrypt
- **JWT tokens**: Stored in localStorage with automatic injection
- **Session management**: Token expiration and refresh handling
- **Response handling**: Support for nested response data structures

### GIS Integration
- **Cesium 3D mapping**: Custom initialization with performance optimizations
- **Layered design**: Map background + UI overlay with CSS pointer-events
- **Real-time positioning**: Device location and status visualization
- **Model analysis**: Gas diffusion, drainage flow simulation
- **Custom styling**: Entities with dynamic animations and popups

### Real-time Systems
- **WebSocket Integration**: Real-time data streaming for monitoring
- **Alarm Management**: Global alarm system with modal notifications
- **Video Integration**: RTSP streaming for surveillance cameras using HLS.js

### Code Generation
Template-based code generator using Handlebars:
- Located in `src/views/admin/system/tools/`
- Generates complete CRUD page sets with API, form, and table configuration
- Visual configuration interface with multi-file download capability

### Build Configuration
- **Development**: Hot reload with proxy and CORS handling
- **Production**: Terser minification, console removal, CSS code splitting disabled
- **Asset handling**: Cesium resources (GLB, WASM files) properly included
- **Chunk optimization**: 2MB warning limit with proper chunking strategy

### Performance Considerations
- Component lazy loading for route-based code splitting
- ECharts auto-resize with requestAnimationFrame
- Memory cleanup in component unmount hooks
- CSS variables for consistent theming
- Optimized table rendering with virtual scrolling patterns
- Responsive design using CSS Grid and Flexbox

### Specialized Features
- **Dual Interface Design**: Screen interface for visualization, Admin interface for management
- **Domain-Specific Analysis**: Risk assessment, model simulation, predictive analytics
- **Multi-tenant Support**: Different configurations for development and production environments
- **Comprehensive Logging**: Request/response logging with error handling
- **Security Focus**: RSA encryption, JWT tokens, and protected routes