<template>
  <div class="component-model-list">
      <div class="model_level" v-if="state.selectedTitle === 1">
          <div
                  v-for="item in modelData"
                  :key="item.title"
                  class="butt-normal"
                  :class="[state.selectedLevel === item.id ? 'butt-active' : '']"
                  @click="handleClickLevel(item.id)"
          >
              {{ item.title }}
          </div>
      </div>
      <HeatingLeakageAnalysis
              v-if=" state.selectedTitle === 1 &&
              state.selectedLevel === 1 &&
              state.showResult"
              :data="state.data"
              @close="handleClose"
      />
      <HeatingExplosionWarningAnalysis
              v-if="state.selectedTitle === 1 &&
              state.selectedLevel === 2 &&
              state.showResult"
              :data="state.data"
              @close="handleClose"
      />
      <HeatingExplosionResultAnalysis
              v-if="state.selectedTitle === 1 &&
              state.selectedLevel === 3 &&
              state.showResult"
              :data="state.data"
              @close="handleClose"
      />
      <HeatingSpaceAnalysis
              v-if="state.selectedLevel === 4 &&
              state.showResult"
              :data="state.data"
              @close="handleClose"
      />
      <HeatingSpaceModel
              v-if="state.selectedLevel === 4"
              :data="state.data"
              @analyze="handleAnalyze"
      />
      <HeatingExplosionResultModel
              v-if="state.selectedTitle === 1 &&
              state.selectedLevel === 3"
              @analyze="handleAnalyze"
      />
      <ExplosionResultLegend
              v-if="state.selectedTitle === 1 &&
              state.selectedLevel === 3"
      />
  </div>
</template>

<script setup>
import bus from "@/utils/mitt.js";
import {onBeforeUnmount, reactive, watch} from "vue";
import {useRoute} from "vue-router";
const route = useRoute();

import HeatingLeakageAnalysis from "./components/HeatingLeakageAnalysis.vue";
import HeatingExplosionWarningAnalysis from "./components/HeatingExplosionWarningAnalysis.vue";
import HeatingExplosionResultAnalysis from "./components/HeatingExplosionResultAnalysis.vue";
import HeatingExplosionResultModel from "./components/HeatingExplosionResultModel.vue";
import ExplosionResultLegend from "./components/ExplosionResultLegend.vue";
import HeatingSpaceModel from "./components/HeatingSpaceModel.vue";
import HeatingSpaceAnalysis from "./components/HeatingSpaceAnalysis.vue";
import {mapStates} from "@/components/GisMap/mapStates.js";

const state = reactive({
    data: {},
    selectedTitle: 0,
    selectedLevel: 1,
    showResult: false,
    formData: {}
});
const modelData = reactive([
    {
        id: 1,
        title: "泄漏溯源分析",
    },
    {
        id: 2,
        title: "爆管预警分析",
    },
    {
        id: 3,
        title: "爆管影响后果分析",
    },
]);

const handleClickLevel = (e) => {
    //todo 激活模型
    state.selectedLevel = e;
    state.showResult = false;
    drawCircleBuffers();
};

const handleClose = () => {
    state.showResult = false;
    drawCircleBuffers();
};

const handleAnalyze = (formData) => {
    state.formData = formData;
    //todo 模型分析 formData
    state.showResult = true;
    drawCircleBuffers();
};

bus.on("misModelAnalysis", (data) => {
    state.data = data;
    if ([1, 2].includes(state.selectedLevel)) {
        state.showResult = true;
    }
    drawCircleBuffers();
});

bus.on("misHeatingSpaceModelAnalysis", (data) => {
    state.data = data;
    drawCircleBuffers();
});

watch(() => route.path,
    (newPath) => {
        //todo 激活模型
        state.selectedTitle = newPath === "/heating/monitoringMis/warning/leak"? 1 :
            newPath === "/heating/decisionMis/support/analysis"? 4 :
            0;
        state.selectedLevel = state.selectedTitle;
        state.showResult = false;
        mapStates.earth.entity.clearLayersByIds(["gasCircleBuffer", "gasCircleBuffer1"]);
    },
    {
        immediate: true,
        deep: true
    }
)

const drawCircleBuffers = () => {
    console.log("drawCircleBuffers", state.formData);
    mapStates.earth.entity.clearLayersByIds(["gasCircleBuffer", "gasCircleBuffer1"]);
    if (state.selectedLevel === 4 &&
        state.data?.longitude &&
        state.data?.latitude &&
        state.showResult) {
        // 空间分析
        mapStates.earth.entity.addCircleGeometryFromDegrees({
            layerId: "gasCircleBuffer",
            position: [state.data?.longitude, state.data?.latitude],
            radius: Number(state.formData?.radius),
            colorStr: "#1775f9",
        });
    } else if (state.selectedTitle === 1 &&
        state.selectedLevel === 3 &&
        state.showResult) {
        // 爆管影响后果分析
        mapStates.earth.entity.addCircleGeometryFromDegrees({
            layerId: "gasCircleBuffer",
            position: [115.099221, 35.292527],
            radius: 200,
            colorStr: "#FF8200",
        });
        mapStates.earth.entity.addCircleGeometryFromDegrees({
            layerId: "gasCircleBuffer1",
            position: [115.099221, 35.292527],
            radius: 50,
            colorStr: "#FF2E01",
        });
    }

    if (state.showResult &&(state.selectedLevel === 4 ||
        (state.selectedTitle === 1 && state.selectedLevel === 3))) {
        window.setTimeout(() => {
            mapStates.earth.entity.focusLayerByLayerId("gasCircleBuffer",{
                height: 1000,
            })
        }, 200);
    }
};

onBeforeUnmount(() => {
    //todo 清理模型分析结果
    mapStates.earth.entity.clearLayersByIds(["gasCircleBuffer", "gasCircleBuffer1"]);
});
</script>

<style lang="scss" scoped>
.component-model-list {
  pointer-events: all;
  width: 178px;

  .model_level2 {
    width: 178px;
  }

  .butt-normal {
    margin-bottom: 10px;
    padding: 8px 10px;
    border-radius: 4px;
    cursor: pointer;
    background: rgba(255, 255, 255, 0.9);
    border: 1px solid rgba(255, 255, 255, 0.9);
    font-size: 14px;
    font-weight: 400;
    color: rgba(0, 0, 0, 0.7);
    text-align: center;

    &:hover {
      background: rgba(26, 142, 231, 0.9);
      border: 1px solid rgba(26, 142, 231, 1);
    }
  }

  .butt-active {
    background: rgba(26, 142, 231, 0.9);
    border: 1px solid rgba(26, 142, 231, 1);
    color: #ffffff;
  }

}
</style>
