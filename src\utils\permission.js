/**
 * 权限管理工具函数
 * 用于根据用户权限动态过滤路由配置
 */

/**
 * 检查用户是否有指定路由的权限
 * @param {string} routeName - 路由名称
 * @param {Array} userPermissions - 用户权限列表
 * @returns {boolean} - 是否有权限
 */
export function hasPermission(routeName, userPermissions) {
  if (!routeName || !userPermissions || !Array.isArray(userPermissions)) {
    return false
  }
  
  // 检查用户权限列表中是否包含该路由名称
  return userPermissions.includes(routeName)
}

/**
 * 递归过滤路由配置，只保留用户有权限的路由
 * @param {Array} routes - 路由配置数组
 * @param {Array} userPermissions - 用户权限列表
 * @returns {Array} - 过滤后的路由配置
 */
export function filterRoutesByPermission(routes, userPermissions) {
  if (!routes || !Array.isArray(routes)) {
    return []
  }
  
  if (!userPermissions || !Array.isArray(userPermissions)) {
    return []
  }
  
  const filteredRoutes = []
  
  for (const route of routes) {
    // 如果路由没有name，保留该路由（通常是布局路由或重定向路由）
    if (!route.name) {
      // 如果有子路由，递归过滤子路由
      if (route.children && route.children.length > 0) {
        const filteredChildren = filterRoutesByPermission(route.children, userPermissions)
        // 如果过滤后还有子路由，保留该路由
        if (filteredChildren.length > 0) {
          filteredRoutes.push({
            ...route,
            children: filteredChildren
          })
        }
      } else {
        // 没有子路由的布局路由也保留
        filteredRoutes.push(route)
      }
      continue
    }
    
    // 检查当前路由权限
    const hasCurrentPermission = hasPermission(route.name, userPermissions)
    
    // 如果当前路由没有权限，直接跳过
    if (!hasCurrentPermission) {
      continue
    }
    
    // 如果有子路由，递归过滤子路由
    if (route.children && route.children.length > 0) {
      const filteredChildren = filterRoutesByPermission(route.children, userPermissions)
      // 添加包含过滤后子路由的路由配置
      filteredRoutes.push({
        ...route,
        children: filteredChildren
      })
    } else {
      // 添加当前路由
      filteredRoutes.push(route)
    }
  }
  
  return filteredRoutes
}

/**
 * 从路由配置中提取所有路由名称
 * @param {Array} routes - 路由配置数组
 * @returns {Array} - 路由名称数组
 */
export function extractRouteNames(routes) {
  const names = []
  
  function traverse(routeList) {
    routeList.forEach(route => {
      if (route.name) {
        names.push(route.name)
      }
      if (route.children && route.children.length > 0) {
        traverse(route.children)
      }
    })
  }
  
  traverse(routes)
  return names
}

/**
 * 检查用户权限并返回可访问的路由配置
 * @param {Array} allRoutes - 所有路由配置
 * @param {Array} userPermissions - 用户权限列表
 * @returns {Array} - 用户可访问的路由配置
 */
export function getAccessibleRoutes(allRoutes, userPermissions) {
  return filterRoutesByPermission(allRoutes, userPermissions)
}

/**
 * 验证路由访问权限（用于路由守卫）
 * @param {string} routeName - 要访问的路由名称
 * @param {Array} userPermissions - 用户权限列表
 * @returns {boolean} - 是否允许访问
 */
export function canAccessRoute(routeName, userPermissions) {
  // 如果没有路由名称，允许访问（通常是布局路由）
  if (!routeName) {
    return true
  }
  
  // 检查用户是否有该路由的权限
  return hasPermission(routeName, userPermissions)
}