<!-- 设备信息 -->
<template>
    <div class="device-detail-curve">
        <div class="curve-tab">
            <div class="cur-tab1">
                <div
                        class="btn"
                        :class="{ 'btn-active': state.type === 1 }"
                        @click="handleSubtabChange(1)"
                >
                    <span>24小时</span>
                </div>
                <div
                        class="btn"
                        :class="{ 'btn-active': state.type === 2 }"
                        @click="handleSubtabChange(2)"
                >
                    <span>7天</span>
                </div>
                <div
                        class="btn"
                        :class="{ 'btn-active': state.type === 3 }"
                        @click="handleSubtabChange(3)"
                >
                    <span>30天</span>
                </div>
            </div>
            <el-select
                v-model="state.selectIndicator"
                placeholder="请选择"
                size="small"
                style="width: 120px"
                popper-class="monitor-curve-select-dropdown"
                @change="handleJczbChange"
            >
                <el-option
                    v-for="item in state.indicators"
                    :key="item.id"
                    :label="item.monitorIndexName"
                    :value="item?.monitorField"
                />
            </el-select>
<!--            <el-date-picker
                    v-model="monthRange"
                    type="daterange"
                    :clearable="false"
                    range-separator="-"
                    start-placeholder="开始时间"
                    end-placeholder="结束时间"
                    format="YYYY-MM-DD"
                    popper-class="screen-popup-date-picker"
                    value-format="YYYY-MM-DD HH:mm:ss"
                    :disabled-date="disabledDate"
                    @calendar-change="chooseDay = $event[0]"
                    @focus="chooseDay = null"
                    @change="handleSubtabChange(4)"
            />-->
        </div>
        <div class="curve-indicators">
            <div>{{ state.title }}</div>
            <div></div>
        </div>
        <div class="curve-echart">
            <SmoothLineChart
                    id="smoothLineCharta"
                    :value="state.chartData"
                    :width="'350px'"
                    :height="'160px'"
                    :redThreshold="state.redThreshold"
                    :orangeThreshold="state.orangeThreshold"
                    :yellowThreshold="state.yellowThreshold"
                    :latestAlarm="state.latestAlarm"
            />
        </div>
    </div>
</template>

<script setup>
import {reactive, ref, watch} from "vue";
import moment from "moment";
import SmoothLineChart from "./SmoothLineChart.vue";
import {
    popupDeviceCurveApiInfo, popupMonitorDeviceAlarmListApiInfo,
    popupMonitorIndicatorsApiInfo, popupMonitorThresholdListApiInfo
} from "@/components/GisMap/popup/popupApi.js";

const props = defineProps({
    baseInfo: {
        type: Array,
        default: () => [],
    },
    data: {
        type: Object,
        default: () => ({}),
    },
});

const state = reactive({
    type: 1, // 1:24小时, 2:7天, 3:30天, 4:自定义
    queryParams: null, // 请求参数
    resData: [], //曲线数据
    chartData: [], // 处理后的曲线数据
    thresholdData: [], //设备指标三级阈值数据
    deviceAlarmData: [], //设备指标告警数据
    title: "", //曲线标题
    indicators: [], // 指标列表
    selectIndicator: "", //选中的指标
    redThreshold: null, //阈值-红色
    orangeThreshold: null, //阈值-橙色
    yellowThreshold: null, //阈值-黄色
    latestAlarm: null, //指标最新告警
});

const monthRange = ref(["", ""]); // 自定义时间范围
const chooseDay = ref(null); // 选择日期

const toCamelCase = (str) => {
    return str.replace(/_([a-z])/g, (_, letter) => letter.toUpperCase());
};

/**
 * 在数组中按时间顺序插入数据
 * @param {Array} dataArray - 要插入数据的数组
 * @param {Object} insertData - 要插入的数据对象
 * @param {string} timeKey - 时间字段名，默认为 'name'
 * @returns {Array} - 插入数据后的新数组
 */
const insertDataByTimeOrder = (dataArray, insertData, timeKey = 'name') => {
    if (!dataArray || !insertData || !insertData[timeKey]) {
        return dataArray;
    }

    const insertTime = moment(insertData[timeKey]);
    let insertIndex = dataArray.length; // 默认插入到最后

    // 找到正确的插入位置，保持时间顺序
    for (let i = 0; i < dataArray.length; i++) {
        const dataTime = moment(dataArray[i][timeKey]);

        // 如果插入时间小于等于当前时间，则在此位置插入
        if (insertTime.isSameOrBefore(dataTime)) {
            insertIndex = i;
            break;
        }
    }

    // 创建新数组并插入数据
    const newArray = [...dataArray];
    newArray.splice(insertIndex, 0, insertData);
    return newArray;
};

//指标切换
const handleJczbChange = (value) => {
    const ind = state.indicators.find((item) => item?.monitorField === value);
    const jczb = ind?.monitorIndexName ?? "";
    state.title = jczb && ind?.type === 0 ? `${jczb}` : jczb ? `${jczb}（${ind?.measureUnit}）` : "";
    let newData = [];
    state.latestAlarm = null;
    state.resData.map((item) => {
        if (item[value] !== -999 && item[value] !== null) {
            newData.push({
                // ...item,
                name: item?.monitorTime,
                value: item[value],
                unit: ind?.measureUnit || "",
                jczb: jczb || "",
                type: ind?.type,
            });
        }
    });

    const selectIndicatorAlarm = state.deviceAlarmData.find(
        (item) => item?.monitorIndex === ind?.monitorIndex
    );
    if (selectIndicatorAlarm && state.queryParams) {
        const alarmTime = moment(selectIndicatorAlarm?.alarmTime);
        const startTime = moment(state.queryParams?.startTime);
        const endTime = moment(state.queryParams?.endTime);

        // const startTime = moment("2025-05-01 00:00:00");
        // const endTime = moment("2030-07-07 18:00:00");

        // 只有当告警时间在查询时间区间内时，才更新latestAlarm
        if (newData.length > 0 && alarmTime.isBetween(startTime, endTime, null, '[]')) {
            // 创建告警数据对象
            const alarmData = {
                name: selectIndicatorAlarm?.alarmTime,
                value: parseFloat(selectIndicatorAlarm?.alarmValue),
                unit: ind?.measureUnit || "",
                jczb: jczb || "",
                type: ind?.type,
            };
            newData = insertDataByTimeOrder(newData, alarmData, 'name');
            // 更新 latestAlarm
            state.latestAlarm = alarmData;
        }
    }

    state.chartData = newData;

    const selectIndicatorThreshold = state.thresholdData.find(
        (item) => item?.monitorIndex === ind?.monitorIndex
    );
    state.redThreshold = selectIndicatorThreshold?.thresholdLevel1Max || null;
    state.orangeThreshold = selectIndicatorThreshold?.thresholdLevel2Max || null;
    state.yellowThreshold = selectIndicatorThreshold?.thresholdLevel3Max || null;
}

/**
 * 日期选择器范围控制
 * @param {*} date
 */
const disabledDate = (date) => {
    if (!chooseDay.value) {
        return false;
    }
    const after30Days = moment(date).isAfter(
        moment(chooseDay.value).add(31, "day")
    );
    const before30Days = moment(date).isBefore(
        moment(chooseDay.value).subtract(30, "day")
    );
    return after30Days || before30Days;
};

// 子tab-echart 24小时/7天/30天/自定义
const handleSubtabChange = (index) => {
    state.type = index;
    state.resData = [];
    if (index !== 4) {
        monthRange.value = ["", ""];
    }
    const params = {
        deviceId: props.data.id,
        startTime:
            index === 4
                ? monthRange.value[0]
                : moment()
                    .subtract(index === 1 ? 1 : index === 2 ? 7 : 30, "day")
                    .format("YYYY-MM-DD HH:mm:ss"),
        endTime:
            index === 4
                ? monthRange.value[1]
                : moment().format("YYYY-MM-DD HH:mm:ss"),
    };
    state.queryParams = params;
    if (popupDeviceCurveApiInfo[props.data?.layerId]) {
        Promise.all([
            popupMonitorIndicatorsApiInfo[props.data?.layerId](props.data?.id),
            popupMonitorThresholdListApiInfo[props.data?.layerId](props.data?.id),
            popupMonitorDeviceAlarmListApiInfo[props.data?.layerId](props.data?.id),
            popupDeviceCurveApiInfo[props.data?.layerId](params)
        ]).then(([indicatorsResponse, thresholdListResponse, deviceAlarmListResponse, curveResponse]) => {
            state.indicators = indicatorsResponse.data;
            state.thresholdData = thresholdListResponse.data;
            state.deviceAlarmData = deviceAlarmListResponse.data;
            state.resData = curveResponse.data;
            if (state.selectIndicator) {
                handleJczbChange(state.selectIndicator);
            } else {
                handleJczbChange(state.selectIndicator = indicatorsResponse.data?.[0]?.monitorField ?? "")
            }
        }).catch((error) => {
            console.error("Error fetching data:", error);
        });
    }
};

watch(
    () => props.data,
    () => {
        handleSubtabChange(2);
    },
    {
        deep: true,
        immediate: true,
    },
);

</script>

<style lang="scss" scoped>
.device-detail-curve {
  overflow-y: auto;
  position: relative;

  .curve-tab {
    /*div:nth-child(1) {
      position: relative;
      &::after {
        content: "";
        position: absolute;
        width: 2px;
        height: 13px;
        background: #999;
        right: -10px;
        opacity: 0.8;
        top: 55%;
        transform: translateY(-50%);
      }
    }*/
    display: flex;
    justify-content: space-between;
    align-items: center; //垂直居中

    .cur-tab1 {
      width: 40%;
      display: flex;
      justify-content: left;
      align-items: center; //垂直居中

      .btn {
        width: 96px;
        height: 30px;
        line-height: 30px;
        background: #152d55;
        border-radius: 2px;
        text-align: center;
        cursor: pointer;

        > span {
          font-size: 12px;
          font-weight: 400;
          color: rgba(255, 255, 255, 0.8);
        }
      }

      .btn-active {
        background: #0d93f7;

        > span {
          color: rgba(255, 255, 255, 0.8);
        }
      }
    }
  }

  :deep(.el-date-editor) {
    width: 260px;
    margin-left: 20px;
    flex-grow: 0 !important;
    background: #152d55;
    border: none;
    box-shadow: none;
    height: 30px;

    .el-range-input,
    .el-range-separator {
      color: #fff;
    }
  }

  .curve-indicators {
    position: inherit;
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-top: 10px;
    font-size: 12px !important;
  }

  .curve-echart {
    margin-top: 10px;
    //height: calc(100% - 46px);
  }
}
</style>

<style lang="scss">
/* 全局样式，用于解决下拉框被遮挡的问题 */
.monitor-curve-select-dropdown {
  z-index: 1000 !important;

  .el-select-dropdown__item {
    font-size: 12px;
  }

}
</style>
