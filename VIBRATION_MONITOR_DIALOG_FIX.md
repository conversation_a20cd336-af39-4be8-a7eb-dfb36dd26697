# 振动监测弹窗修复说明

## 问题描述

在 `src/views/admin/bridge/monitoring/realtime/vibration.vue` 页面中，操作列的"监测曲线"和"运行记录"按钮点击后弹出同一个弹窗 `VibrationMonitorDialog.vue`，存在与静态响应监测弹窗和动态响应监测弹窗相同的问题：

1. **默认tab显示问题**：点击"监测曲线"按钮弹窗打开时有时显示正确的监测曲线tab，有时显示错误
2. **监测曲线DOM加载问题**：监测曲线初始化加载后，选择时间范围切换后无法正确加载监测曲线DOM
3. **tab切换数据加载问题**：监测曲线和运行记录tab切换时数据加载不及时

## 修复内容

### 1. 修复默认tab显示逻辑

**修复方案**：
- 优化 `watch(() => props.defaultTab)` 监听器，添加条件判断避免重复设置，并使用 `immediate: true`
- 优化 `watch(() => props.visible)` 监听器，确保弹窗显示时正确设置默认tab
- 修改 `handleClose` 函数，不重置 `activeTab`，让它保持状态以便下次使用正确的默认值

### 2. 修复监测曲线DOM加载问题

**修复方案**：
- 在 `renderChart` 函数中添加更严格的DOM检查和错误处理
- 使用 `setTimeout` 添加额外延迟确保DOM完全准备好
- 在时间范围和指标切换时主动清理图表实例，强制重新创建
- 添加图表容器可见性检查，避免在不可见状态下渲染

### 3. 优化tab切换数据加载

**修复方案**：
- 优化 `handleTabChange` 函数，添加延迟确保tab内容完全显示
- 改进 `watch(activeTab)` 监听器，添加更完善的数据加载逻辑
- 在切换到监测曲线tab时检查数据状态，必要时重新获取数据

## 具体修复代码

### 1. 默认tab显示逻辑修复

```javascript
// 监听defaultTab变化
watch(() => props.defaultTab, (newTab) => {
  if (newTab && newTab !== activeTab.value) {
    activeTab.value = newTab;
    console.log('设置默认tab:', newTab);
  }
}, { immediate: true });

// 监听对话框显示
watch(() => props.visible, (newVal) => {
  if (newVal) {
    // 确保在弹窗显示时正确设置默认tab
    if (props.defaultTab && props.defaultTab !== activeTab.value) {
      activeTab.value = props.defaultTab;
      console.log('弹窗显示时设置默认tab:', props.defaultTab);
    }
    
    if (props.deviceInfo.id) {
      fetchMonitorIndicators();
      calculateTableMaxHeight();
    }
  }
});
```

### 2. 监测曲线DOM加载问题修复

```javascript
// 渲染图表
const renderChart = (data) => {
  if (!chartRef.value || !Array.isArray(data) || !currentIndicatorInfo.value) {
    console.warn('渲染图表条件不满足:', {
      hasChartRef: !!chartRef.value,
      hasData: Array.isArray(data),
      hasIndicatorInfo: !!currentIndicatorInfo.value
    });
    return;
  }

  // 使用nextTick确保DOM已经更新，并添加额外的延迟确保图表容器完全准备好
  nextTick(() => {
    setTimeout(() => {
      // 检查图表容器是否仍然存在且可见
      if (!chartRef.value || !chartRef.value.offsetWidth || !chartRef.value.offsetHeight) {
        console.warn('图表容器不可见或尺寸为0，延迟重试');
        setTimeout(() => renderChart(data), 100);
        return;
      }

      // 如果图表实例存在但容器已改变，重新初始化
      if (chartInstance && chartInstance.getDom() !== chartRef.value) {
        chartInstance.dispose();
        chartInstance = null;
      }

      if (!chartInstance) {
        chartInstance = echarts.init(chartRef.value);
        console.log('初始化新的图表实例');
      }

      chartInstance.setOption(option);
      chartInstance.resize();
    }, 50); // 添加50ms延迟确保DOM完全准备好
  });
};
```

### 3. 时间范围和指标切换优化

```javascript
const handleTimeRangeChange = async (value) => {
  console.log('时间范围切换:', value);
  if (value) {
    timeRange.value = value;
  }
  // 清理现有图表实例，确保重新渲染
  if (chartInstance) {
    chartInstance.dispose();
    chartInstance = null;
  }
  if (selectedIndicator.value) {
    const timeRangeData = getTimeRange(timeRange.value);
    await fetchCurveData(timeRangeData);
  }
};

const handleIndicatorChange = async (value) => {
  console.log('指标切换:', value);
  if (value) {
    selectedIndicator.value = value;
    // 更新当前指标信息
    const indicator = monitorIndicators.value.find(item => item.monitorField === value);
    if (indicator) {
      currentIndicatorInfo.value = indicator;
      // 清理现有图表实例，确保重新渲染
      if (chartInstance) {
        chartInstance.dispose();
        chartInstance = null;
      }
    }
  }
  if (timeRange.value) {
    const timeRangeData = getTimeRange(timeRange.value);
    await fetchCurveData(timeRangeData);
  }
};
```

### 4. tab切换数据加载优化

```javascript
// 事件处理
const handleTabChange = (tab) => {
  console.log('Tab切换:', tab);
  
  if (tab === 'curve') {
    // 切换到监测曲线tab
    if (monitorIndicators.value.length > 0) {
      nextTick(() => {
        // 延迟一点时间确保tab内容完全显示
        setTimeout(() => {
          if (chartInstance) {
            chartInstance.resize();
          } else if (chartData.value.length > 0) {
            // 如果图表实例不存在但有数据，重新渲染
            renderChart(chartData.value);
          }
        }, 100);
      });
    }
  } else if (tab === 'records') {
    // 切换到运行记录tab
    fetchRecords();
  }
};
```

## 修复验证

✅ **构建测试通过**：代码已通过 `npm run build` 构建测试，无语法错误
✅ **类型检查通过**：所有TypeScript类型检查通过
✅ **代码质量**：清理了未使用的变量和导入

## 修复完成状态

所有问题已修复完成：

1. ✅ **默认tab显示逻辑修复完成**
2. ✅ **监测曲线DOM加载问题修复完成**
3. ✅ **tab切换数据加载优化完成**

## 使用说明

修复后的振动监测弹窗现在应该能够：

1. **正确显示默认tab**：点击"监测曲线"按钮时显示监测曲线tab，点击"运行记录"按钮时显示运行记录tab
2. **稳定的图表渲染**：监测曲线在时间范围切换时能正确重新渲染
3. **流畅的tab切换**：在监测曲线和运行记录之间切换时数据能及时加载

建议在实际环境中进行完整的功能测试以验证修复效果。
