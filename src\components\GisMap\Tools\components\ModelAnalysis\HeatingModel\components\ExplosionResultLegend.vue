<template>
  <div class="component-legend">
    <div class="title-header">图例</div>
    <div class="content-list">
        <div v-for="(item, i) in legendData" :key="i" class="child-item">
            <img v-if="item.icon !== 'default' &&
            item.icon !== 'circle' &&
            item.icon !== 'line'"
            :src="item.icon" alt="" />
            <span v-else :class="item.icon" :style="{ background: item.color }"></span>
            <div class="text-label">
                {{ item.label }}
            </div>
        </div>
    </div>
  </div>
</template>

<script setup>
import {computed} from "vue";

const legendData = computed(() => {
  return [
      {
          id: "risk1",
          label: "热水扩散范围",
          icon: "circle",
          color: "#FF2E01",
      },
      {
          id: "risk2",
          label: "爆管影响范围",
          icon: "circle",
          color: "#FF8200",
      },
  ];
});
</script>

<style lang="scss" scoped>
.component-legend {
  position: absolute;
  bottom: 0;
  left: 10vw;
  pointer-events: all;
  width: 138px;
  min-height: 30px;
  max-height: 200px;
  background: rgba(13, 37, 82, 0.8);
  border-radius: 4px;
  border: 1px solid #324256;
  display: flex;
  flex-direction: column;
  padding: 15px 8px 15px 12px;
  margin-bottom: 8px;
  .title-header {
    color: #fff;
    font-size: 14px;
    font-weight: 800;
    padding-bottom: 12px;
  }
  .content-list {
    flex: 1;
    overflow: auto;

    .child-title {
      color: #cae1f8;
      font-size: 14px;
      font-weight: 600;
      margin-bottom: 8px;
    }

    .child-special {
      color: #cae1f8;
      font-size: 13px;
      font-weight: 400;
      margin-bottom: 8px;
      padding-left: 5px;
    }

    .child-item {
      padding-left: 12px;
      display: flex;
      align-items: center;
      margin-bottom: 8px;

      .default {
        width: 9px;
        height: 9px;
        margin:0px 9px 0px 0px;
        transform: skew(-20deg);
      }

      .circle {
        width: 15px;
        height: 15px;
        margin:0px 9px 0px 0px;
        border-radius: 50%;
      }

      .line {
        width: 15px;
        height: 5px;
        margin:0px 9px 0px 0px;
      }

      .text-label {
        font-size: 12px;
        font-weight: 400;
        color: #cae1f8;
      }

      img {
        width: 18px;
        height: 18px;
        margin-right: 5px;
      }
    }
  }
}
</style>
