<template>
  <PopupFrame
    :title="'隐患详情'"
    :hasArrow="true"
    :showTitle="showTitle"
    :data="data"
    :tabList="tabList"
    @change-tab="tabIndex = $event"
    @close="handleClose"
  >
    <component
      :is="currentCom"
      :data="data"
      :baseInfo="baseInfo"
      :tabIndex="tabIndex"
    />
  </PopupFrame>
</template>
<script setup>
import { ref, computed, watch } from "vue";
import PopupFrame from "../PopupFrame.vue";
import BaseInfo from "../BaseInfo.vue";
import DisposalProcess from "./components/DisposalProcess.vue";
import {mapStates} from "@/components/GisMap/mapStates.js";
import {popupApiInfo} from "@/components/GisMap/popup/popupApi.js";
import {popupConfigInfo} from "@/components/GisMap/popup/NormalDetail/config.js";
const props = defineProps({
    showTitle: {
        type: Boolean,
        default: true,
    },
    data: {
        type: Object,
        default: () => ({}),
    },
    closeEvent: {
        type: Function,
        required: false
    }
});

// tab
const tabList = computed(() => {
    let defaultTab = [
        {
            id: 1,
            name: "隐患信息",
            com: BaseInfo,
        },
        {
            id: 2,
            name: "隐患流程",
            com: DisposalProcess,
        },
    ];
    return defaultTab;
});
const tabIndex = ref(1);

const currentCom = computed(() => {
    return tabList.value.find((v) => v.id === tabIndex.value)?.com;
});

const baseInfo = ref([]);

// 获取基础信息
const getBaseInfo = async () => {
    const { data } = await popupApiInfo[props.data?.layerId](props.data?.id);
    baseInfo.value = popupConfigInfo[props.data?.layerId].map((v) => {
        let fileList = [];
        if (v.isImg || v.isFile) {
            const fileMap = data[v.fileProp] ? v.isImg ?
                data[v.fileProp].split(",") : data[v.fileProp] : [];
            fileList = fileMap.map((f) => {
                return {
                    url: v.isImg ? f : f.fileUrl,
                    name: v.isImg ? f : f.fileName,
                };
            });
        }
        return {
            ...v,
            value: data[v.props] || "",
            fileList: v.isImg || v.isFile ? fileList : [],
        };
    });
};

const handleClose = () => {
    props.closeEvent?.();
    //清除高亮
    mapStates.earth.entity.clearHighlight();
};

watch(
    () => props.data,
    () => {
        getBaseInfo();
    },
    {
        deep: true,
        immediate: true,
    }
);
</script>

<style lang="scss" scoped>
:deep(.el-table) {
  --el-table-border-color: none;
  --el-table-bg-color: none;
  tbody tr {
    &:hover {
      td {
        background: #12254c !important;
      }
    }
  }
}
</style>
