<template>
    <div class="heating-explosion-result-analysis">
        <div class="analysis-header">
            <div class="title">爆管分析结果</div>
            <div class="close-btn" @click="$emit('close')">
                <el-icon>
                    <Close/>
                </el-icon>
            </div>
        </div>

        <div class="content-area">
            <!-- 停水关阀方案 -->
            <div class="pipeline-section">
                <div class="section-title">停水关阀方案</div>
                <div class="pipeline-list">
                    <div
                            v-for="valve in state.valves"
                            :key="valve.id"
                            class="pipeline-item"
                            @click="handlePipelineClick(valve)"
                    >
                        {{ valve.name }}
                    </div>
                </div>
            </div>
            <div class="pipeline-section">
                <div class="section-title">影响管线</div>
                <div class="impact-list">
                    <div class="impact-item">
                        <span class="impact-text">1000m</span>
                    </div>
                </div>
            </div>
            <div class="pipeline-section">
                <div class="section-title">热水扩散范围</div>
                <div class="impact-list">
                    <div class="impact-item">
                        <span class="impact-text">20㎡</span>
                    </div>
                </div>
            </div>
        </div>
    </div>
</template>

<script setup>
import {onMounted, reactive, ref, watch} from 'vue'
import {Close} from '@element-plus/icons-vue'
import {popupApiInfo, popupMonitorAlarmApiInfo} from "@/components/GisMap/popup/popupApi.js";
import {alarmLevelColorMap} from "@/components/GisMap/popup/device.js";
import MonitorCurve from "./MonitorCurve.vue";

const props = defineProps({
    data: {
        type: Object,
        default: () => ({}),
    }
})

const state = reactive({
    valves: [
        {id: 1, name: 'FM-23567T6'},
        {id: 2, name: 'FM-23567T7'}
    ]
});

const emit = defineEmits(['close']);

const handleValveClick = (valve) => {
    // todo 点击阀门处理逻辑
}

onMounted(() => {
});
</script>

<style lang="scss" scoped>
.heating-explosion-result-analysis {
  position: absolute;
  top: 160px;
  right: 3vw;
  width: 400px;
  display: flex;
  flex-direction: column;
  border-radius: 8px;
  pointer-events: all;

  &::before {
    content: "";
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    backdrop-filter: blur(10px);
    background: rgba(11, 30, 65, 0.8);
    border-radius: 8px;
    border: 1px solid #19385c;
    z-index: -1;
  }

  .analysis-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    border-top-left-radius: 12px;
    padding: 10px 20px;
    font-size: 16px;
    font-weight: bold;
    color: #ffffff;
    border-bottom: 1px solid #19385c;

    .title {
      font-size: 16px;
      font-weight: bold;
      color: #ffffff;
    }

    .close-btn {
      font-size: 20px;
      cursor: pointer;
      color: #ffffff;

      &:hover {
        opacity: 0.8;
      }
    }
  }

  .content-area {
    flex: 1;
    padding: 10px 20px;
    overflow: auto;

    .alarm-section, .pipeline-section {
      margin-bottom: 20px;
    }

    .pipeline-section {
      .section-title {
        font-size: 14px;
        font-weight: bold;
        color: #ffffff;
        margin-bottom: 12px;
      }

      .impact-list {
            display: flex;
            flex-direction: column;
            gap: 8px;

            .impact-item {
                display: flex;
                align-items: center;

                .impact-text {
                    color: #ffffff;
                    font-size: 12px;
                }
            }
        }
    }

    .alarm-section {
      .section-header {
        display: flex;
        align-items: center;
        justify-content: space-between;
        margin-bottom: 10px;
        border-bottom: 1px solid #19385c;
        padding-bottom: 10px;

        .section-title {
          font-size: 14px;
          font-weight: bold;
          color: #ffffff;
        }

        .tab-container {
          display: flex;
          align-items: center;

          .tab-item {
            color: rgba(255, 255, 255, 0.8);
            font-size: 13px;
            letter-spacing: 1px;
            position: relative;
            margin-left: 20px;
            cursor: pointer;

            &::after {
              content: "";
              position: absolute;
              left: 50%;
              transform: translateX(-50%);
              bottom: -10px;
              width: 0;
              height: 2px;
              background: #fff;
              opacity: 0;
              border-radius: 1px;
              transition: all 0.2s ease;
            }

            &.active {
              position: relative;
              color: #fff;

              &:after {
                opacity: 1;
                width: 40px;
              }
            }
          }
        }
      }

      .device-info {
        .info-row {
          display: flex;
          margin-bottom: 8px;

          .label {
            color: rgba(255, 255, 255, 0.8);
            font-size: 12px;
            min-width: 70px;
            text-align: right;
          }

          .value {
            color: #ffffff;
            font-size: 12px;

            &.alarm-level, &.alarm-value {
              color: #ff4757;
              font-weight: bold;
            }
          }
        }
      }

      .curve-info {
        .curve-placeholder {
          height: 120px;
          display: flex;
          align-items: center;
          justify-content: center;
          color: rgba(255, 255, 255, 0.6);
          font-size: 12px;
          border: 1px dashed rgba(255, 255, 255, 0.3);
          border-radius: 4px;
        }
      }
    }

    .pipeline-section {
      .pipeline-list {
        .pipeline-item {
          color: #4a9eff;
          font-size: 12px;
          cursor: pointer;
          margin-bottom: 6px;

          &:hover {
            text-decoration: underline;
          }
        }
      }
    }
  }
}
</style>