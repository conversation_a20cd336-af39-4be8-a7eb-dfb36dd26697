<template>
  <div class="component-model-list">
      <div class="model_level"  v-if="state.selectedTitle === 1">
          <div
                  v-for="item in modelData"
                  :key="item.title"
                  class="butt-normal"
                  :class="[state.selectedLevel === item.id ? 'butt-active' : '']"
                  @click="handleClickLevel(item.id)"
          >
              {{ item.title }}
          </div>
      </div>
      <StructuralBottleneckAnalysis
          v-if="state.selectedTitle === 1 &&
      state.selectedLevel === 1" />
      <PipeSiltationAnalysis
          v-if="state.selectedTitle === 1 &&
          state.selectedLevel === 2" />
      <PipeOverflowModel
              v-if="state.selectedTitle === 1 &&
              state.selectedLevel === 3"
              @analyze="handleAnalyze"
      />
      <PumpStationModel
              v-if="state.selectedTitle === 1 &&
              state.selectedLevel === 4"
              @analyze="handleAnalyze"
      />
      <WaterloggingModel
              v-if="state.selectedTitle === 5"
              @analyze="handleAnalyze"
      />
      <StructuralLegend
          v-if="state.selectedTitle === 1 &&
      state.selectedLevel === 1" />
      <SiltationLegend
          v-if="state.selectedTitle === 1 &&
      state.selectedLevel === 2" />
  </div>
</template>

<script setup>
import bus from "@/utils/mitt.js";
import {reactive, watch} from "vue";
import StructuralBottleneckAnalysis from "./components/StructuralBottleneckAnalysis.vue";
import PipeSiltationAnalysis from "./components/PipeSiltationAnalysis.vue";
import PipeOverflowModel from "./components/PipeOverflowModel.vue";
import PumpStationModel from "./components/PumpStationModel.vue";
import WaterloggingModel from "./components/WaterloggingModel.vue";
import StructuralLegend from "./components/StructuralLegend.vue";
import SiltationLegend from "./components/SiltationLegend.vue";
import {useRoute} from "vue-router";
const route = useRoute();

const state = reactive({
    selectedTitle: 0,
    selectedLevel: 1,
});
const modelData = reactive([
    {
        id: 1,
        title: "结构瓶颈模拟分析",
    },
    {
        id: 2,
        title: "管网淤积预测预警分析",
    },
    {
        id: 3,
        title: "管网溢流预测预警分析",
    },
    {
        id: 4,
        title: "泵站负荷能力分析",
    },
]);

const handleClickLevel = (e) => {
    //todo 激活模型
    state.selectedLevel = e;
};

const handleAnalyze = (formData) => {
    //todo 模型分析 formData
};

watch(() => route.path,
    (newPath) => {
        //todo 激活模型
        state.selectedTitle = newPath === "/drainage/predict/warning/network" ? 1 :
            newPath === "/drainage/predict/warning/flooding" ? 5 : 0;
        state.selectedLevel = state.selectedTitle;
    },
    {
        immediate: true,
        deep: true
    }
)

</script>

<style lang="scss" scoped>
.component-model-list {
  pointer-events: all;
  width: 178px;

  .model_level2 {
    width: 178px;
  }

  .butt-normal {
    margin-bottom: 10px;
    padding: 8px 10px;
    border-radius: 4px;
    cursor: pointer;
    background: rgba(255, 255, 255, 0.9);
    border: 1px solid rgba(255, 255, 255, 0.9);
    font-size: 14px;
    font-weight: 400;
    color: rgba(0, 0, 0, 0.7);
    text-align: center;

    &:hover {
      background: rgba(26, 142, 231, 0.9);
      border: 1px solid rgba(26, 142, 231, 1);
    }
  }

  .butt-active {
    background: rgba(26, 142, 231, 0.9);
    border: 1px solid rgba(26, 142, 231, 1);
    color: #ffffff;
  }

}
</style>
