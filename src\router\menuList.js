// 从admin.js中抽离的所有菜单项
// 格式：{name: 'routeName', title: '菜单标题'}

export const menuList = [
  // 燃气模块
  { name: 'GasHome', title: '燃气首页' },
  { name: 'GasInfo', title: '燃气信息资源管理' },
  { name: 'GasInfoResource', title: '燃气信息资源管理' },
  { name: 'GasInfoResourceNetwork', title: '燃气管网基础信息管理' },
  { name: 'GasInfoResourceStation', title: '燃气场站基础信息管理' },
  { name: 'GasInfoResourceManhole', title: '窨井基础信息管理' },
  { name: 'GasInfoResourceDanger', title: '危险源信息查询' },
  { name: 'GasInfoResourceProtection', title: '防护目标信息查询' },
  { name: 'GasInfoResourceMonitor', title: '燃气监测设备信息查询' },
  { name: 'GasRisk', title: '燃气管网爆炸风险评估' },
  { name: 'GasRiskExplosion', title: '燃气管网爆炸风险评估' },
  { name: 'GasRiskExplosionAssessment', title: '燃气管网爆炸风险评估管理' },
  { name: 'GasRiskExplosionHeatmap', title: '燃气管网爆炸风险热力图' },
  { name: 'GasRiskExplosionStationAssessment', title: '燃气场站爆炸风险评估' },
  { name: 'GasRiskExplosionStationHeatmap', title: '燃气场站爆炸风险热力图' },
  { name: 'GasLeak', title: '燃气泄漏实时监测与报警' },
  { name: 'GasLeakMonitor', title: '燃气泄漏实时监测与报警' },
  { name: 'GasLeakMonitorThreshold', title: '报警阈值管理' },
  { name: 'GasLeakMonitorVideo', title: '视频监控监测' },
  { name: 'GasLeakMonitorDevice', title: '设备运行监测' },
  { name: 'GasLeakMonitorAlert', title: '燃气泄漏报警提醒' },
  { name: 'GasLeakMonitorAnalysis', title: '燃气泄漏报警分析' },
  { name: 'GasLeakMonitorDisposal', title: '燃气泄漏报警处置' },
  { name: 'GasPredict', title: '燃气泄漏爆炸预测预警' },
  { name: 'GasPredictWarning', title: '燃气泄漏爆炸预测预警' },
  { name: 'GasPredictWarningSource', title: '可燃气体泄漏溯源分析' },
  { name: 'GasPredictWarningDiffusion', title: '可燃气体扩散范围分析' },
  { name: 'GasPredictWarningDamage', title: '爆炸损伤范围分析' },
  { name: 'GasDecision', title: '燃气泄漏爆炸辅助决策' },
  { name: 'GasDecisionSupport', title: '燃气泄漏爆炸辅助决策' },
  { name: 'GasDecisionSupportPlan', title: '燃气泄漏处置方案管理' },
  { name: 'GasDecisionSupportStatistics', title: '燃气综合统计分析' },
  { name: 'GasDecisionSupportReport', title: '燃气安全运行评估报告' },

  // 排水模块
  { name: 'DrainageHome', title: '排水首页' },
  { name: 'DrainageBasic', title: '排水基础数据管理' },
  { name: 'DrainageBasicData', title: '排水基础数据管理' },
  { name: 'DrainageBasicDataNetwork', title: '管网基础信息管理' },
  { name: 'DrainageBasicDataOutlet', title: '排水口基础信息管理' },
  { name: 'DrainageBasicDataGrate', title: '雨水篦子基础信息管理' },
  { name: 'DrainageBasicDataManhole', title: '排水窨井基础信息管理' },
  { name: 'DrainageBasicDataPump', title: '排水泵站基础信息管理' },
  { name: 'DrainageBasicDataCctv', title: 'CCTV检测信息管理' },
  { name: 'DrainageBasicDataPlant', title: '污水厂基础信息管理' },
  { name: 'DrainageBasicDataFlooding', title: '易涝点基础信息管理' },
  { name: 'DrainageBasicDataDevice', title: '监测设备基础信息管理' },
  { name: 'DrainageBasicDataStatistics', title: '排水基础数据统计分析' },
  { name: 'DrainageRisk', title: '排水风险隐患管理' },
  { name: 'DrainageRiskManagement', title: '排水风险隐患管理' },
  { name: 'DrainageRiskManagementNetwork', title: '排水管网风险评估' },
  { name: 'DrainageRiskManagementPlant', title: '污水厂风险评估' },
  { name: 'DrainageRiskManagementPump', title: '排水泵站风险评估' },
  { name: 'DrainageRiskManagementHidden', title: '排水隐患信息管理' },
  { name: 'DrainageMonitoring', title: '排水监测报警管理' },
  { name: 'DrainageMonitoringAlarm', title: '排水监测报警管理' },
  { name: 'DrainageMonitoringAlarmThreshold', title: '报警阈值管理' },
  { name: 'DrainageMonitoringAlarmVideo', title: '视频监控监测' },
  { name: 'DrainageMonitoringAlarmFlow', title: '管网流量监测' },
  { name: 'DrainageMonitoringAlarmOverflow', title: '污水溢流监测' },
  { name: 'DrainageMonitoringAlarmGas', title: '可燃气体积累监测' },
  { name: 'DrainageMonitoringAlarmWater', title: '易涝点积水监测' },
  { name: 'DrainageMonitoringAlarmQuality', title: '排污水质监测' },
  { name: 'DrainageMonitoringAlarmCover', title: '井盖状态监测' },
  { name: 'DrainageMonitoringAlarmInfo', title: '排水报警信息管理' },
  { name: 'DrainageMonitoringAlarmDisposal', title: '排水报警信息处置' },
  { name: 'DrainageMonitoringAlarmAnalysis', title: '排水报警统计分析' },
  { name: 'DrainagePredict', title: '预测预警分析' },
  { name: 'DrainagePredictWarning', title: '预测预警分析' },
  { name: 'DrainagePredictWarningNetwork', title: '排水管网模型预测预警' },
  { name: 'DrainagePredictWarningFlooding', title: '内涝模型预测预警' },
  { name: 'DrainageDecision', title: '辅助决策支持' },
  { name: 'DrainageDecisionSupport', title: '辅助决策支持' },
  { name: 'DrainageDecisionSupportFlood', title: '防汛调度辅助决策' },
  { name: 'DrainageDecisionSupportMaintenance', title: '管网运维改造辅助决策' },
  { name: 'DrainageDecisionSupportReport', title: '排水安全风险评估报告' },
  { name: 'DrainageDecisionSupportEmergency', title: '排水应急事件管理' },
  { name: 'DrainageDecisionSupportMaterial', title: '防汛物资管理' },
  { name: 'DrainageDecisionSupportDanger', title: '危险源信息管理' },
  { name: 'DrainageDecisionSupportProtection', title: '防护目标信息管理' },

  // 供热模块
  { name: 'HeatingHome', title: '供热首页' },
  { name: 'HeatingBasic', title: '供热基础信息管理' },
  { name: 'HeatingBasicData', title: '供热基础信息管理' },
  { name: 'HeatingBasicDataEnterprise', title: '供热企业信息管理' },
  { name: 'HeatingBasicDataSource', title: '热源信息管理' },
  { name: 'HeatingBasicDataStation', title: '换热站信息管理' },
  { name: 'HeatingBasicDataNetwork', title: '管网信息管理' },
  { name: 'HeatingBasicDataUnit', title: '机组信息管理' },
  { name: 'HeatingBasicDataBuilding', title: '供热区域建筑信息管理' },
  { name: 'HeatingBasicDataUser', title: '供热用户信息管理' },
  { name: 'HeatingBasicDataManhole', title: '供热窨井信息管理' },
  { name: 'HeatingBasicDataDevice', title: '供热设备信息管理' },
  { name: 'HeatingBasicDataStatistics', title: '基础数据统计分析' },
  { name: 'HeatingRisk', title: '供热风险管理' },
  { name: 'HeatingRiskManagement', title: '供热风险管理' },
  { name: 'HeatingRiskManagementNetwork', title: '供热管网风险信息' },
  { name: 'HeatingRiskManagementStation', title: '供热场站风险信息' },
  { name: 'HeatingRiskManagementHidden', title: '供热隐患信息管理' },
  { name: 'HeatingRiskManagementAccident', title: '供热安全事故信息管理' },
  { name: 'HeatingRiskManagementDanger', title: '危险源信息管理' },
  { name: 'HeatingRiskManagementProtection', title: '防护目标信息管理' },
  { name: 'HeatingRiskManagementNetworkDistribution', title: '供热管网风险分布' },
  { name: 'HeatingRiskManagementStationDistribution', title: '供热场站风险分布' },
  { name: 'HeatingRiskManagementHiddenDistribution', title: '供热隐患分布' },
  { name: 'HeatingRiskManagementProtectionDistribution', title: '防护目标分布' },
  { name: 'HeatingMonitoring', title: '供热安全监测预警' },
  { name: 'HeatingMonitoringWarning', title: '供热安全监测预警' },
  { name: 'HeatingMonitoringWarningThreshold', title: '监测预警阈值管理' },
  { name: 'HeatingMonitoringWarningNetwork', title: '管网运行监测预警管理' },
  { name: 'HeatingMonitoringWarningSource', title: '热源运行安全风险防控' },
  { name: 'HeatingMonitoringWarningStation', title: '换热站运行安全风险防控' },
  { name: 'HeatingMonitoringWarningLeak', title: '供热管网泄漏研判分析' },
  { name: 'HeatingMonitoringWarningPlan', title: '预案管理' },
  { name: 'HeatingDecision', title: '辅助决策支持' },
  { name: 'HeatingDecisionSupport', title: '辅助决策支持' },
  { name: 'HeatingDecisionSupportLevel', title: '态势数据分级管理' },
  { name: 'HeatingDecisionSupportReport', title: '态势数据报送' },
  { name: 'HeatingDecisionSupportAnalysis', title: '空间分析' },
  { name: 'HeatingDecisionSupportDisplay', title: '态势标绘与展示' },

  // 桥梁模块
  { name: 'BridgeHome', title: '桥梁首页' },
  { name: 'BridgeBasic', title: '桥梁基础数据' },
  { name: 'BridgeBasicData', title: '桥梁基础数据' },
  { name: 'BridgeBasicDataAsset', title: '桥梁资产管理' },
  { name: 'BridgeBasicDataComponent', title: '桥梁构件管理' },
  { name: 'BridgeBasicDataPoint', title: '桥梁布点方案' },
  { name: 'BridgeDevice', title: '桥梁设备管理' },
  { name: 'BridgeDeviceManagement', title: '桥梁设备管理' },
  { name: 'BridgeDeviceManagementInfo', title: '桥梁设备信息' },
  { name: 'BridgeDeviceManagementGroup', title: '设备分组管理' },
  { name: 'BridgeMonitoring', title: '桥梁实时监测' },
  { name: 'BridgeMonitoringRealtime', title: '桥梁实时监测' },
  { name: 'BridgeMonitoringRealtimeVideo', title: '桥梁视频监控' },
  { name: 'BridgeMonitoringRealtimeEnvironment', title: '环境数据监测与分析' },
  { name: 'BridgeMonitoringRealtimeStatic', title: '静态响应监测与分析' },
  { name: 'BridgeMonitoringRealtimeDynamic', title: '动态响应监测与分析' },
  { name: 'BridgeMonitoringRealtimeVibration', title: '意外震动监测与分析' },
  { name: 'BridgeMonitoringRealtimeTraffic', title: '交通荷载监测与分析' },
  { name: 'BridgeAlarm', title: '桥梁监测报警管理' },
  { name: 'BridgeAlarmManagement', title: '桥梁监测报警管理' },
  { name: 'BridgeAlarmManagementThreshold', title: '监测报警阈值管理' },
  { name: 'BridgeAlarmManagementInfo', title: '桥梁报警信息管理' },
  { name: 'BridgeAlarmManagementDisposal', title: '桥梁报警信息处置' },
  { name: 'BridgeInspection', title: '桥梁检测养护管理' },
  { name: 'BridgeInspectionMaintenance', title: '桥梁检测养护管理' },
  { name: 'BridgeInspectionMaintenancePlan', title: '检测养护计划管理' },
  { name: 'BridgeInspectionMaintenanceRecord', title: '检测养护记录管理' },
  { name: 'BridgeInspectionMaintenanceReport', title: '检测报告管理' },
  { name: 'BridgeInspectionMaintenanceData', title: '维修养护数据管理' },
  { name: 'BridgeInspectionMaintenanceDisease', title: '病害数据管理' },
  { name: 'BridgeAnalysis', title: '桥梁数据分析' },
  { name: 'BridgeAnalysisData', title: '桥梁数据分析' },
  { name: 'BridgeAnalysisDataExtreme', title: '极值分析' },
  { name: 'BridgeAnalysisDataComparison', title: '对比分析' },
  { name: 'BridgeAnalysisDataCorrelation', title: '关联分析' },
  { name: 'BridgeAnalysisDataMultiChannel', title: '多通道分析' },
  { name: 'BridgeAnalysisDataDeflection', title: '挠度分析' },
  { name: 'BridgeAnalysisDataMultiDevice', title: '多设备频谱分析' },
  { name: 'BridgeAnalysisDataMultiPeriod', title: '多时段频谱分析' },
  { name: 'BridgeAnalysisDataFilter', title: '滤波分析' },
  { name: 'BridgeStatistics', title: '桥梁数据统计分析' },
  { name: 'BridgeStatisticsAnalysis', title: '桥梁数据统计分析' },
  { name: 'BridgeStatisticsAnalysisInfo', title: '桥梁信息统计与分析' },
  { name: 'BridgeStatisticsAnalysisAlarm', title: '报警信息统计与分析' },
  { name: 'BridgeStatisticsAnalysisSafety', title: '安全评估统计与分析' },
  { name: 'BridgeStatisticsAnalysisOverload', title: '超载数据统计与分析' },
  { name: 'BridgeStatisticsAnalysisMonitoring', title: '监测数据统计与分析' },
  { name: 'BridgeSafety', title: '桥梁安全评估' },
  { name: 'BridgeSafetyAssessment', title: '桥梁安全评估' },
  { name: 'BridgeSafetyAssessmentReport', title: '安全评估报告' },
  { name: 'BridgeSafetyAssessmentScore', title: '桥梁安全评分' },

  // 综合模块
  { name: 'ComprehensiveIndustry', title: '行业综合监管' },
  { name: 'ComprehensiveIndustrySupervision', title: '行业综合监管' },
  { name: 'ComprehensiveIndustrySupervisionNotice', title: '通知公告' },
  { name: 'ComprehensiveIndustrySupervisionPlanning', title: '规划建设' },
  { name: 'ComprehensiveIndustrySupervisionInspection', title: '督查督办' },
  { name: 'ComprehensiveIndustrySupervisionEvaluation', title: '考核评价' },
  { name: 'ComprehensiveIndustrySupervisionExpert', title: '专家咨询' },
  { name: 'ComprehensiveIndustrySupervisionResource', title: '资料中心' },
  { name: 'ComprehensivePublic', title: '面向公众服务' },
  { name: 'ComprehensivePublicService', title: '面向公众服务' },
  { name: 'ComprehensivePublicServiceAlert', title: '报警通报' },
  { name: 'ComprehensivePublicServiceReport', title: '公众报警' },
  { name: 'ComprehensiveEvent', title: '事件分析研判' },
  { name: 'ComprehensiveEventAnalysis', title: '事件分析研判' },
  { name: 'ComprehensiveEventAnalysisManagement', title: '应急事件管理' },
  { name: 'ComprehensiveEventAnalysisStatistics', title: '应急事件统计' },
  { name: 'ComprehensiveCoordination', title: '协同联动处置' },
  { name: 'ComprehensiveCoordinationDisposal', title: '协同联动处置' },
  { name: 'ComprehensiveCoordinationDisposalWarning', title: '预警信息管理' },
  { name: 'ComprehensiveCoordinationDisposalManage', title: '预警信息处置' },
  { name: 'ComprehensiveRisk', title: '综合风险管控' },
  { name: 'ComprehensiveRiskControl', title: '综合风险管控' },
  { name: 'ComprehensiveRiskControlHidden', title: '隐患排查治理' },
  { name: 'ComprehensiveRiskControlAssessment', title: '风险评估标识' },
  { name: 'ComprehensiveRiskControlArea', title: '风险区域划分' },
  { name: 'ComprehensiveRiskControlMap', title: '风险四色图' },
  { name: 'ComprehensiveEmergency', title: '应急联动管理' },
  { name: 'ComprehensiveEmergencyLinkage', title: '应急联动管理' },
  { name: 'ComprehensiveEmergencyLinkagePlan', title: '应急预案管理' },
  { name: 'ComprehensiveEmergencyLinkageResource', title: '应急资源管理' },
  { name: 'ComprehensiveEmergencyLinkageExpert', title: '应急专家管理' },
  { name: 'ComprehensiveEquipment', title: '设备运维管理' },
  { name: 'ComprehensiveEquipmentMaintenance', title: '设备运维管理' },
  { name: 'ComprehensiveEquipmentMaintenanceDuty', title: '值班管理' },
  { name: 'ComprehensiveEquipmentMaintenanceInspection', title: '巡检管理' },
  { name: 'ComprehensiveEquipmentMaintenanceOrder', title: '巡检工单' },
  { name: 'ComprehensiveEquipmentMaintenanceRepair', title: '设备维修' },
  { name: 'ComprehensiveEquipmentMaintenanceMonitor', title: '设备监控' },

  // 系统管理模块
  { name: 'SystemManagement', title: '系统管理' },
  { name: 'SystemManagementAdmin', title: '系统管理' },
  { name: 'SystemManagementAdminUser', title: '用户管理' },
  { name: 'SystemManagementAdminOrganization', title: '组织管理' },
  { name: 'SystemManagementAdminRole', title: '角色管理' },
  { name: 'SystemManagementAdminLoginLog', title: '登陆日志' }
]

// 按模块分组的菜单列表
export const menuListByModule = {
  gas: menuList.filter(item => item.name.startsWith('Gas')),
  drainage: menuList.filter(item => item.name.startsWith('Drainage')),
  heating: menuList.filter(item => item.name.startsWith('Heating')),
  bridge: menuList.filter(item => item.name.startsWith('Bridge')),
  comprehensive: menuList.filter(item => item.name.startsWith('Comprehensive')),
  system: menuList.filter(item => item.name.startsWith('System'))
}

// 获取所有包含'Mis'的菜单项（管理信息系统相关）
export const misMenuList = menuList.filter(item => 
  item.name.includes('Mis') || 
  item.name.includes('Management') || 
  item.name.includes('Admin')
)

export default menuList