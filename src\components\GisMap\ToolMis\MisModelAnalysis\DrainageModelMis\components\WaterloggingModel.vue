<template>
    <div class="open-space-diffusion-model">
        <div class="analysis-header">
            <div class="title">内涝预测预警分析</div>
        </div>

        <div class="content-area">
            <div class="form-container">
                <div class="form-row">
                    <div class="form-group">
                        <label class="form-label">城市DEM：</label>
                        <span class="form-text">自动获取</span>
                    </div>
                    <div class="form-group">
                        <label class="form-label">监测数据：</label>
                        <span class="form-text">自动获取（流量、雨量、液位等）</span>
                    </div>
                </div>

                <div class="form-row">
                    <div class="form-group">
                        <label class="form-label">管网数据：</label>
                        <span class="form-text">自动获取</span>
                    </div>
                    <div class="form-group">
                        <label class="form-label">*降雨区域：</label>
                        <el-select v-model="formData.rainArea" placeholder="请选择降雨区域" class="form-select">
                            <el-option
                                v-for="option in rainAreaOptions"
                                :key="option.value"
                                :label="option.label"
                                :value="option.value"
                            />
                        </el-select>
                    </div>
                </div>

                <div class="form-row">
                    <div class="form-group">
                        <label class="form-label">*降雨时间：</label>
                        <el-select v-model="formData.rainTime" placeholder="请选择降雨时间" class="form-select">
                            <el-option
                                v-for="option in rainTimeOptions"
                                :key="option.value"
                                :label="option.label"
                                :value="option.value"
                            />
                        </el-select>
                    </div>
                    <div class="form-group">
                        <label class="form-label">*降雨量(mm/h)：</label>
                        <el-input v-model="formData.rainfall"
                                  placeholder="请输入降雨量"
                                  class="form-input"
                                  size="small"/>
                    </div>
                </div>

                <div class="form-row">
                    <div class="form-group">
                        <label class="form-label">*降雨强度(mm/h)：</label>
                        <el-input v-model="formData.rainIntensity"
                                  placeholder="请输入降雨强度"
                                  class="form-input"
                                  size="small"/>
                    </div>
                    <div class="form-group">
                        <label class="form-label">*风向(°)：</label>
                        <el-input v-model="formData.windDirection"
                                  placeholder="请输入风向角度"
                                  class="form-input"
                                  size="small"/>
                    </div>
                </div>

                <div class="form-row">
                    <div class="form-group">
                        <label class="form-label">*风速(m/s)：</label>
                        <el-input v-model="formData.windSpeed"
                                  placeholder="请输入风速"
                                  class="form-input"
                                  size="small"/>
                    </div>
                    <div class="form-group">
                        <label class="form-label">*道路渗透系数：</label>
                        <el-input v-model="formData.roadPermeability"
                                  placeholder="请输入道路渗透系数"
                                  class="form-input"
                                  size="small"/>
                    </div>
                </div>

                <div class="form-actions">
                    <el-button type="primary" class="analyze-btn" @click="handleAnalyze">
                        开始分析
                    </el-button>
                </div>
            </div>
        </div>
    </div>
</template>

<script setup>
import { reactive } from 'vue'
import { Close } from '@element-plus/icons-vue'

const emit = defineEmits(['close', 'analyze'])

// 选项数据
const rainAreaOptions = [
    { label: '城关街道', value: '1' },
    { label: '渔沃街道', value: '2' },
    { label: '东明集镇', value: '3' },
    { label: '刘楼镇', value: '4' },
    { label: '陆圈镇', value: '5' },
    { label: '马头镇', value: '6' },
    { label: '三春集镇', value: '7' },
    { label: '大屯镇', value: '8' },
    { label: '武胜桥镇', value: '9' },
    { label: '菜园集镇', value: '10' },
    { label: '沙窝镇', value: '11' },
    { label: '小井镇', value: '12' },
    { label: '长兴集乡', value: '13' },
    { label: '焦园乡', value: '14' }
];

const rainTimeOptions = [
    { label: '凌晨', value: 'early-morning' },
    { label: '上午', value: 'morning' },
    { label: '下午', value: 'afternoon' },
    { label: '晚上', value: 'evening' },
    { label: '全天', value: 'all-day' }
];

const formData = reactive({
    rainArea: '1',
    rainTime: 'morning',
    rainfall: '',
    rainIntensity: '',
    windDirection: '',
    windSpeed: '',
    roadPermeability: ''
});

const handleAnalyze = () => {
    // 将表单数据传递给父组件
    emit('analyze', formData)
};
</script>

<style lang="scss" scoped>
.open-space-diffusion-model {
  position: fixed;
  top: 18vh;
  right: 2vw;
  width: 700px;
  display: flex;
  flex-direction: column;
  border-radius: 8px;
  pointer-events: all;

  &::before {
    content: "";
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    backdrop-filter: blur(10px);
    background: rgba(255, 255, 255, 1);
    border-radius: 8px;
    border: 1px solid rgba(255, 255, 255, 0.4);
    z-index: -1;
  }

  .analysis-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    border-top-left-radius: 12px;
    padding: 10px 20px;
    font-size: 16px;
    font-weight: bold;
    color: rgba(50, 50, 50, 0.8);
    border-bottom: 1px solid rgba(200, 200, 200, 0.4);

    .title {
      font-size: 16px;
      font-weight: bold;
      color: rgba(50, 50, 50, 0.8);
    }

    .close-btn {
      font-size: 22px;
      cursor: pointer;
      color: rgba(50, 50, 50, 0.8);

      &:hover {
        opacity: 0.8;
      }
    }
  }

  .content-area {
    flex: 1;
    padding: 10px 20px;
    overflow: auto;

    .form-container {
      .form-row {
        display: flex;
        gap: 10px;
        margin-bottom: 15px;

          .form-group {
           flex: 1;
           display: flex;
           flex-direction: row;
           align-items: center;
           gap: 10px;

           .form-label {
             color: rgba(50, 50, 50, 0.8);
             font-size: 12px;
             font-weight: 500;
             width: 120px;
             text-align: right;
             white-space: nowrap;
           }

           .form-text {
              color: rgba(50, 50, 50, 0.6);
              font-size: 12px;
           }

           .form-select {
              width: 180px;
              height: 32px;
              :deep(.el-input__wrapper) {
                background: rgba(200, 200, 200, 0.5);
                border: 1px solid rgba(200, 200, 200, 0.2);
                border-radius: 4px;
                box-shadow: none;

                &:hover {
                  border-color: rgba(255, 255, 255, 0.4);
                }

                &.is-focus {
                  border-color: #409eff;
                  box-shadow: 0 0 0 1px rgba(64, 158, 255, 0.2);
                }
              }

              :deep(.el-input__inner) {
                color: rgba(50, 50, 50, 0.8);
                font-size: 12px;

                &::placeholder {
                  color: rgba(50, 50, 50, 0.4);
                }
              }

              :deep(.el-select__caret) {
                color: rgba(50, 50, 50, 0.8);
              }
            }

            .form-input {
              width: 180px;
              height: 32px;
              :deep(.el-input__wrapper) {
                background: rgba(200, 200, 200, 0);
                border: 1px solid rgba(200, 200, 200, 0.5);
                border-radius: 4px;
                box-shadow: none;

                &:hover {
                  border-color: rgba(255, 255, 255, 0);
                }

                &.is-focus {
                  border-color: #409eff;
                  box-shadow: 0 0 0 1px rgba(64, 158, 255, 0.2);
                }
              }

              :deep(.el-input__inner) {
                color: rgba(50, 50, 50, 0.8);
                font-size: 12px;

                &::placeholder {
                  color: rgba(50, 50, 50, 0.4);
                }
              }
            }
        }
      }

      .form-actions {
        display: flex;
        justify-content: center;
        margin-top: 20px;

        .analyze-btn {
          background: linear-gradient(135deg, #409eff 0%, #1890ff 100%);
          border: none;
          border-radius: 6px;
          padding: 16px 24px;
          font-size: 14px;
          font-weight: 500;
          color: #ffffff;
          cursor: pointer;
          transition: all 0.3s ease;

          &:hover {
            background: linear-gradient(135deg, #66b1ff 0%, #40a9ff 100%);
            transform: translateY(-1px);
            box-shadow: 0 4px 12px rgba(64, 158, 255, 0.3);
          }

          &:active {
            transform: translateY(0);
          }
        }
      }
    }
  }
}

:deep(.el-select-dropdown) {
  background: rgba(11, 30, 65, 0.95);
  border: 1px solid #19385c;
  backdrop-filter: blur(10px);

  .el-select-dropdown__item {
    color: rgba(255, 255, 255, 0.9);
    font-size: 12px;

    &:hover {
      background: rgba(255, 255, 255, 0.1);
    }

    &.selected {
      background: #409eff;
      color: #ffffff;
    }
  }
}
</style>