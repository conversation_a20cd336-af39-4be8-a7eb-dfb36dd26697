<template>
  <div class="modern-pie-container">
    <div class="chart-content">
      <!-- 图表区域 -->
      <div class="chart-section">
        <!-- 中心数据显示 -->
        <div v-if="totalVisible" class="center-info">
          <div class="total-value">{{ totalValue }}</div>
          <div class="total-label">总数</div>
        </div>
        
        <!-- 图表容器 -->
        <div ref="chartContainer" class="chart-container"></div>
      </div>
      
      <!-- 图例区域 -->
      <div v-if="showLegend" class="legend-section">
        <div class="legend-container">
          <div 
            v-for="(item, index) in chartData" 
            :key="item.name"
            class="legend-item"
            :class="{ active: selectedIndex === index, hovered: hoveredIndex === index }"
            @mouseenter="handleLegendHover(index)"
            @mouseleave="handleLegendLeave()"
            @click="handleLegendClick(index)"
          >
            <span 
              class="legend-dot" 
              :style="{ backgroundColor: item.color }"
            ></span>
            <span class="legend-text">{{ item.name }}</span>
            <span class="legend-value">{{ item.value }}座</span>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted, onUnmounted, watch, nextTick } from 'vue'
import * as echarts from 'echarts'

const props = defineProps({
  data: {
    type: Array,
    required: true,
    default: () => []
  },
  height: {
    type: [String, Number],
    default: '170px'
  },
  totalVisible: {
    type: Boolean,
    default: true
  },
  showLegend: {
    type: Boolean,
    default: true
  },
  // 内径比例
  innerRadius: {
    type: String,
    default: '45%'
  },
  // 外径比例
  outerRadius: {
    type: String,
    default: '75%'
  }
})

const chartContainer = ref(null)
let chartInstance = null
const selectedIndex = ref(-1)
const hoveredIndex = ref(-1)

// 判断是否有数据
const hasData = computed(() => {
  return props.data && props.data.length > 0
})

// 计算总数
const totalValue = computed(() => {
  return props.data.reduce((sum, item) => sum + item.value, 0)
})

// 处理图表数据，添加渐变色
const chartData = computed(() => {
  return props.data.map((item, index) => {
    const baseColor = item.itemStyle?.color || item.color || getDefaultColor(index)
    return {
      name: item.name,
      value: item.value,
      color: baseColor,
      itemStyle: {
        color: {
          type: 'linear',
          x: 0, y: 0, x2: 0, y2: 1,
          colorStops: [
            { offset: 0, color: baseColor },
            { offset: 1, color: adjustColorBrightness(baseColor, -0.2) }
          ]
        },
        borderColor: 'rgba(0, 0, 0, 0.1)',
        borderWidth: 1,
        shadowBlur: 8,
        shadowColor: 'rgba(0, 0, 0, 0.3)',
        shadowOffsetY: 2
      }
    }
  })
})

// 默认颜色配置
const defaultColors = [
  '#36a4e2', '#32d396', '#5d76f9', '#ffc75a',
  '#ff6d28', '#fb3737', '#9c27b0', '#00bcd4'
]

// 获取默认颜色
const getDefaultColor = (index) => {
  return defaultColors[index % defaultColors.length]
}

// 调整颜色亮度
const adjustColorBrightness = (color, amount) => {
  const usePound = color[0] === '#'
  const col = usePound ? color.slice(1) : color
  const num = parseInt(col, 16)
  let r = (num >> 16) + amount * 255
  let g = (num >> 8 & 0x00FF) + amount * 255
  let b = (num & 0x0000FF) + amount * 255
  r = r > 255 ? 255 : r < 0 ? 0 : r
  g = g > 255 ? 255 : g < 0 ? 0 : g
  b = b > 255 ? 255 : b < 0 ? 0 : b
  return (usePound ? '#' : '') + (r << 16 | g << 8 | b).toString(16).padStart(6, '0')
}

// 初始化图表
const initChart = async () => {
  await nextTick()
  if (!chartContainer.value) return
  
  if (chartInstance) {
    chartInstance.dispose()
  }
  
  chartInstance = echarts.init(chartContainer.value)
  updateChart()
  
  // 绑定事件
  chartInstance.on('mouseover', handleChartHover)
  chartInstance.on('mouseout', handleChartLeave)
  chartInstance.on('click', handleChartClick)
  
  window.addEventListener('resize', handleResize)
}

// 更新图表
const updateChart = () => {
  if (!chartInstance) return
  
  const option = {
    backgroundColor: 'transparent',
    tooltip: {
      trigger: 'item',
      formatter: (params) => {
        const percent = ((params.value / totalValue.value) * 100).toFixed(1)
        return `
          <div style="padding: 8px 12px; background: rgba(0, 0, 0, 0.8); border-radius: 6px; border: 1px solid rgba(255, 255, 255, 0.1);">
            <div style="color: #fff; font-size: 14px; font-weight: 500; margin-bottom: 4px;">${params.name}</div>
            <div style="color: rgba(255, 255, 255, 0.8); font-size: 12px;">
              数量: <span style="color: #fff; font-weight: 500;">${params.value}座</span>
            </div>
            <div style="color: rgba(255, 255, 255, 0.8); font-size: 12px;">
              占比: <span style="color: #fff; font-weight: 500;">${percent}%</span>
            </div>
          </div>
        `
      },
      backgroundColor: 'transparent',
      borderWidth: 0,
      textStyle: {
        color: '#fff'
      }
    },
    grid: {
      left: 0,
      right: 0,
      top: 0,
      bottom: 0
    },
    series: [{
      name: '桥梁统计',
      type: 'pie',
      radius: [props.innerRadius, props.outerRadius],
      center: ['50%', '50%'],
      avoidLabelOverlap: false,
      padAngle: 2,
      itemStyle: {
        borderRadius: 4
      },
      label: {
        show: false
      },
      labelLine: {
        show: false
      },
      emphasis: {
        scale: true,
        scaleSize: 8,
        itemStyle: {
          shadowBlur: 15,
          shadowOffsetX: 0,
          shadowOffsetY: 3,
          shadowColor: 'rgba(0, 0, 0, 0.4)'
        }
      },
      data: chartData.value
    }],
    animationType: 'scale',
    animationEasing: 'elasticOut',
    animationDelay: (idx) => idx * 100
  }
  
  chartInstance.setOption(option, true)
}

// 图表悬停事件
const handleChartHover = (params) => {
  hoveredIndex.value = params.dataIndex
}

// 图表离开事件
const handleChartLeave = () => {
  hoveredIndex.value = -1
}

// 图表点击事件
const handleChartClick = (params) => {
  selectedIndex.value = selectedIndex.value === params.dataIndex ? -1 : params.dataIndex
}

// 图例悬停事件
const handleLegendHover = (index) => {
  hoveredIndex.value = index
  if (chartInstance) {
    chartInstance.dispatchAction({
      type: 'highlight',
      seriesIndex: 0,
      dataIndex: index
    })
  }
}

// 图例离开事件
const handleLegendLeave = () => {
  hoveredIndex.value = -1
  if (chartInstance) {
    chartInstance.dispatchAction({
      type: 'downplay',
      seriesIndex: 0
    })
  }
}

// 图例点击事件
const handleLegendClick = (index) => {
  selectedIndex.value = selectedIndex.value === index ? -1 : index
  if (chartInstance) {
    chartInstance.dispatchAction({
      type: 'pieSelect',
      seriesIndex: 0,
      dataIndex: index
    })
  }
}

// 窗口大小调整
const handleResize = () => {
  if (chartInstance) {
    chartInstance.resize()
  }
}

// 监听数据变化
watch(() => props.data, () => {
  if (chartInstance) {
    updateChart()
  }
}, { deep: true })

// 组件挂载
onMounted(() => {
  initChart()
})

// 组件卸载
onUnmounted(() => {
  if (chartInstance) {
    chartInstance.dispose()
    chartInstance = null
  }
  window.removeEventListener('resize', handleResize)
})
</script>

<style scoped>
.modern-pie-container {
  width: 100%;
  height: v-bind(height);
  position: relative;
}

.chart-content {
  display: flex;
  height: 100%;
  align-items: center;
  gap: 40px;
}

.chart-section {
  flex: 1;
  position: relative;
  height: 100%;
  min-width: 0;
}

.chart-container {
  width: 100%;
  height: 100%;
  min-height: 120px;
}

.center-info {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  text-align: center;
  pointer-events: none;
  z-index: 10;
}

.total-value {
  font-family: 'D-DIN', 'D-DIN';
  font-weight: bold;
  font-size: 20px;
  color: #FFFFFF;
  line-height: 22px;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

.total-label {
  font-family: 'PingFangSC', 'PingFang SC';
  font-weight: 400;
  font-size: 12px;
  color: rgba(255, 255, 255, 0.8);
  margin-top: 2px;
}

.legend-section {
  width: 120px;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
  margin-right: 40px;
}

.legend-container {
  display: flex;
  flex-direction: column;
  gap: 8px;
  width: 100%;
}

.legend-item {
  display: flex;
  align-items: center;
  gap: 6px;
  padding: 6px 8px;
  border-radius: 4px;
  cursor: pointer;
  transition: all 0.3s ease;
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid transparent;
  width: 100%;
  min-height: 24px;
}

.legend-item:hover,
.legend-item.hovered {
  background: rgba(255, 255, 255, 0.1);
  border-color: rgba(255, 255, 255, 0.2);
  transform: translateX(2px);
}

.legend-item.active {
  background: rgba(54, 164, 226, 0.2);
  border-color: rgba(54, 164, 226, 0.5);
}

.legend-dot {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  flex-shrink: 0;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.3);
}

.legend-text {
  font-family: 'PingFangSC', 'PingFang SC';
  font-weight: 400;
  font-size: 12px;
  color: rgba(255, 255, 255, 0.9);
  flex: 1;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.legend-value {
  font-family: 'D-DIN', 'D-DIN';
  font-weight: 500;
  font-size: 12px;
  color: #FFFFFF;
  flex-shrink: 0;
}

/* 暂无数据状态样式 */
.no-data-container {
  display: flex;
  height: 100%;
  align-items: center;
  gap: 40px;
}

.no-data-chart {
  flex: 1;
  position: relative;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  min-width: 0;
}

.empty-ring {
  width: 120px;
  height: 120px;
  border: 8px solid rgba(255, 255, 255, 0.1);
  border-radius: 50%;
  position: relative;
  background: linear-gradient(135deg, rgba(54, 164, 226, 0.1), rgba(50, 211, 150, 0.1));
  box-shadow: 
    inset 0 0 20px rgba(255, 255, 255, 0.05),
    0 0 20px rgba(54, 164, 226, 0.1);
}

.empty-ring::before {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 60px;
  height: 60px;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.05);
  border: 2px solid rgba(255, 255, 255, 0.1);
}

.no-data-info {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  text-align: center;
  pointer-events: none;
  z-index: 10;
}

.no-data-icon {
  font-size: 24px;
  margin-bottom: 8px;
  opacity: 0.6;
  animation: pulse 2s infinite;
}

.no-data-text {
  font-family: 'PingFangSC', 'PingFang SC';
  font-weight: 500;
  font-size: 14px;
  color: rgba(255, 255, 255, 0.8);
  margin-bottom: 4px;
}

.no-data-desc {
  font-family: 'PingFangSC', 'PingFang SC';
  font-weight: 400;
  font-size: 12px;
  color: rgba(255, 255, 255, 0.5);
}

.empty-legend-section {
  width: 120px;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
  margin-right: 40px;
}

.empty-legend-item {
  display: flex;
  align-items: center;
  gap: 6px;
  padding: 6px 8px;
  border-radius: 4px;
  background: rgba(255, 255, 255, 0.03);
  border: 1px solid rgba(255, 255, 255, 0.1);
  width: 100%;
  min-height: 24px;
  opacity: 0.6;
}

.empty-legend-dot {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  flex-shrink: 0;
  background: rgba(255, 255, 255, 0.2);
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.empty-legend-text {
  font-family: 'PingFangSC', 'PingFang SC';
  font-weight: 400;
  font-size: 12px;
  color: rgba(255, 255, 255, 0.5);
  flex: 1;
}

@keyframes pulse {
  0%, 100% {
    opacity: 0.6;
    transform: scale(1);
  }
  50% {
    opacity: 0.8;
    transform: scale(1.05);
  }
}

/* 响应式调整 */
@media (max-width: 1366px) {
  .total-value {
    font-size: 18px;
  }
  
  .legend-section {
    width: 100px;
    margin-right: 30px;
  }
  
  .chart-content,
  .no-data-container {
    gap: 30px;
  }
  
  .legend-text,
  .legend-value {
    font-size: 11px;
  }
  
  .legend-item {
    padding: 5px 6px;
    min-height: 22px;
  }
  
  .empty-ring {
    width: 100px;
    height: 100px;
    border-width: 6px;
  }
  
  .empty-ring::before {
    width: 50px;
    height: 50px;
  }
  
  .no-data-icon {
    font-size: 20px;
  }
  
  .no-data-text {
    font-size: 12px;
  }
  
  .no-data-desc {
    font-size: 10px;
  }
  
  .empty-legend-section {
    width: 100px;
    margin-right: 30px;
  }
}

@media (max-height: 768px) {
  .total-value {
    font-size: 16px;
  }
  
  .total-label {
    font-size: 10px;
  }
  
  .legend-section {
    width: 90px;
    margin-right: 20px;
  }
  
  .chart-content,
  .no-data-container {
    gap: 20px;
  }
  
  .legend-item {
    padding: 4px 5px;
    min-height: 20px;
    gap: 4px;
  }
  
  .legend-text,
  .legend-value {
    font-size: 10px;
  }
  
  .legend-dot {
    width: 6px;
    height: 6px;
  }
  
  .empty-ring {
    width: 80px;
    height: 80px;
    border-width: 5px;
  }
  
  .empty-ring::before {
    width: 40px;
    height: 40px;
  }
  
  .no-data-icon {
    font-size: 16px;
    margin-bottom: 4px;
  }
  
  .no-data-text {
    font-size: 10px;
    margin-bottom: 2px;
  }
  
  .no-data-desc {
    font-size: 8px;
  }
  
  .empty-legend-section {
    width: 90px;
    margin-right: 20px;
  }
  
  .empty-legend-text {
    font-size: 10px;
  }
}

@media screen and (min-width: 2561px) {
  .total-value {
    font-size: 24px;
    line-height: 26px;
  }
  
  .total-label {
    font-size: 14px;
  }
  
  .legend-text,
  .legend-value {
    font-size: 12px;
  }
}
</style>