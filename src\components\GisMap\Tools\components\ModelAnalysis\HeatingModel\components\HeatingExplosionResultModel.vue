<template>
    <div class="open-space-diffusion-model">
        <div class="analysis-header">
            <div class="title">爆管影响后果分析</div>
        </div>

        <div class="content-area">
            <div class="form-container">
                <div class="form-row">
                    <div class="form-group">
                        <label class="form-label">爆管管线：</label>
                        <el-input v-model="formData.pipeId"
                                  placeholder="请输入管线编号"
                                  class="form-input"
                                  size="small" />
                    </div>
                </div>

                <div class="form-actions">
                    <el-button type="primary" class="analyze-btn" @click="handleAnalyze">
                        分析估算
                    </el-button>
                </div>
            </div>
        </div>
    </div>
</template>

<script setup>
import { reactive } from 'vue'

const emit = defineEmits(['analyze'])

const formData = reactive({
    pipeId: '*********',
});

const handleAnalyze = () => {
    // 将表单数据传递给父组件
    emit('analyze', formData)
};
</script>

<style lang="scss" scoped>
.open-space-diffusion-model {
  position: absolute;
  top: 0px;
  right: 3vw;
  width: 400px;
  display: flex;
  flex-direction: column;
  border-radius: 8px;
  pointer-events: all;

  &::before {
    content: "";
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    backdrop-filter: blur(10px);
    background: rgba(11, 30, 65, 0.8);
    border-radius: 8px;
    border: 1px solid #19385c;
    z-index: -1;
  }

  .analysis-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    border-top-left-radius: 12px;
    padding: 10px 20px;
    font-size: 16px;
    font-weight: bold;
    color: #ffffff;
    border-bottom: 1px solid #19385c;

    .title {
      font-size: 16px;
      font-weight: bold;
      color: #ffffff;
    }

    .close-btn {
      font-size: 22px;
      cursor: pointer;
      color: #ffffff;

      &:hover {
        opacity: 0.8;
      }
    }
  }

  .content-area {
    flex: 1;
    padding: 10px 20px;
    overflow: auto;

    .form-container {
      .form-row {
        display: flex;
        gap: 10px;
        margin-bottom: 15px;

          .form-group {
           display: flex;
           flex-direction: row;
           align-items: center;
           gap: 10px;
           width: 100%;

           .form-label {
             color: rgba(255, 255, 255, 0.9);
             font-size: 12px;
             font-weight: 500;
             width: 120px;
             text-align: right;
             white-space: nowrap;
           }

           .form-input {
              width: 200px;
              height: 32px;
              :deep(.el-input__wrapper) {
                background: rgba(255, 255, 255, 0.1);
                border: 1px solid rgba(255, 255, 255, 0.2);
                border-radius: 4px;
                box-shadow: none;

                &:hover {
                  border-color: rgba(255, 255, 255, 0.4);
                }

                &.is-focus {
                  border-color: #409eff;
                  box-shadow: 0 0 0 1px rgba(64, 158, 255, 0.2);
                }
              }

              :deep(.el-input__inner) {
                color: #ffffff;
                font-size: 12px;

                &::placeholder {
                  color: rgba(255, 255, 255, 0.5);
                }
              }
            }

            .radio-group {
              display: flex;
              gap: 15px;
              align-items: center;

              :deep(.el-radio) {
                margin-right: 0;

                .el-radio__label {
                  color: rgba(255, 255, 255, 0.9);
                  font-size: 12px;
                }

                .el-radio__input {
                  .el-radio__inner {
                    background: rgba(255, 255, 255, 0.1);
                    border: 1px solid rgba(255, 255, 255, 0.2);

                    &:hover {
                      border-color: rgba(255, 255, 255, 0.4);
                    }
                  }

                  &.is-checked {
                    .el-radio__inner {
                      background: #409eff;
                      border-color: #409eff;
                    }
                  }
                }
              }

              :deep(.el-radio-group) {
                display: flex;
                gap: 15px;
                align-items: center;
              }
            }
        }
      }

      .form-actions {
        display: flex;
        justify-content: center;
        margin-top: 20px;

        .analyze-btn {
          background: linear-gradient(135deg, #409eff 0%, #1890ff 100%);
          border: none;
          border-radius: 6px;
          padding: 16px 24px;
          font-size: 14px;
          font-weight: 500;
          color: #ffffff;
          cursor: pointer;
          transition: all 0.3s ease;

          &:hover {
            background: linear-gradient(135deg, #66b1ff 0%, #40a9ff 100%);
            transform: translateY(-1px);
            box-shadow: 0 4px 12px rgba(64, 158, 255, 0.3);
          }

          &:active {
            transform: translateY(0);
          }
        }
      }
    }
  }
}

:deep(.el-select-dropdown) {
  background: rgba(11, 30, 65, 0.95);
  border: 1px solid #19385c;
  backdrop-filter: blur(10px);

  .el-select-dropdown__item {
    color: rgba(255, 255, 255, 0.9);
    font-size: 12px;

    &:hover {
      background: rgba(255, 255, 255, 0.1);
    }

    &.selected {
      background: #409eff;
      color: #ffffff;
    }
  }
}
</style>