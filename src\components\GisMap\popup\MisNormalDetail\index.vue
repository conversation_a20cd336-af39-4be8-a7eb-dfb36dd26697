<template>
  <MisPopupFrame :title="'查看详情'"
              :hasArrow="true"
              :showTitle="showTitle"
              :data="data"
              @close="handleClose"
  >
    <component :is="currentCom" :data="data" :baseInfo="baseInfo" />
  </MisPopupFrame>
</template>

<script setup>
import {computed, ref, watch} from "vue";
import MisPopupFrame from "../MisPopupFrame.vue";
import BaseInfo from "../MisBaseInfo.vue";
import { popupConfigInfo } from "./config.js";
import {popupApiInfo} from "@/components/GisMap/popup/popupApi.js";
import {mapStates} from "@/components/GisMap/mapStates.js";

const props = defineProps({
    showTitle: true,
    data: {
        type: Object,
        default: () => ({}),
    },
    closeEvent: {
        type: Function,
        required: false
    }
});

const currentCom = computed(() => {
  return BaseInfo;
});

const baseInfo = ref([]);

// 获取基础信息
const getBaseInfo = async () => {
    const { data } = await popupApiInfo[props.data?.layerId](props.data?.id);
    baseInfo.value = popupConfigInfo[props.data?.layerId].map((v) => {
        return {
            ...v,
            value: v.props === 'isMajor' && data[v.props] ==='0' ? '否':
                v.props === 'isMajor' && data[v.props] ==='1' ? '是': data[v.props] || "",
        };
    });
};

const handleClose = () => {
    props.closeEvent?.();
    //清除高亮
    mapStates.earth.entity.clearHighlight();
};
watch(
    () => props.data,
    () => {
        getBaseInfo();
    },
    {
        deep: true,
        immediate: true,
    }
);
</script>

<style lang="scss" scoped>

</style>
