<template>
    <div class="open-space-diffusion-model">
        <div class="analysis-header">
            <div class="title">开放空间致灾后果预测</div>
            <div class="close-btn" @click="$emit('close')">
                <el-icon>
                    <Close/>
                </el-icon>
            </div>
        </div>

        <div class="content-area">
            <div class="form-container">
                <div class="form-row">
                    <div class="form-group">
                        <label class="form-label">可燃气体混合物喷射直径(m)：</label>
                        <el-input v-model="formData.jetDiameter"
                                  placeholder="请输入喷射直径"
                                  class="form-input"
                                  size="small" />
                    </div>
                </div>

                <div class="form-row">
                    <div class="form-group">
                        <label class="form-label">管道内介质压力(MPa)：</label>
                        <el-input v-model="formData.pipelinePressure"
                                  placeholder="请输入管道压力"
                                  class="form-input"
                                  size="small" />
                    </div>
                </div>

                <div class="form-row">
                    <div class="form-group">
                        <label class="form-label">泄漏孔形状：</label>
                        <div class="radio-group">
                            <el-radio-group v-model="formData.leakShape" class="radio-group">
                                <el-radio label="circular" class="radio-item">圆形</el-radio>
                                <el-radio label="triangular" class="radio-item">三角形</el-radio>
                                <el-radio label="rectangular" class="radio-item">长方形</el-radio>
                            </el-radio-group>
                        </div>
                    </div>
                </div>

                <div class="form-row">
                    <div class="form-group">
                        <label class="form-label">泄漏孔大小(m)：</label>
                        <el-input v-model="formData.leakSize"
                                  placeholder="请输入泄漏孔大小"
                                  class="form-input"
                                  size="small" />
                    </div>
                </div>

                <div class="form-row">
                    <div class="form-group">
                        <label class="form-label">泄漏孔面积(m²)：</label>
                        <el-input v-model="formData.leakArea"
                                  placeholder="请输入泄漏孔面积"
                                  class="form-input"
                                  size="small"/>
                    </div>
                </div>

                <div class="form-row">
                    <div class="form-group">
                        <label class="form-label">可燃气体温度T(k)：</label>
                        <el-input v-model="formData.gasTemperature"
                                  placeholder="请输入气体温度"
                                  class="form-input"
                                  size="small" />
                    </div>
                </div>

                <div class="form-row">
                    <div class="form-group">
                        <label class="form-label">环境温度(k)：</label>
                        <el-input v-model="formData.environmentalTemperature"
                                  placeholder="请输入环境温度"
                                  class="form-input"
                                  size="small" />
                    </div>
                </div>

                <div class="form-actions">
                    <el-button type="primary" class="analyze-btn" @click="handleAnalyze">
                        开始分析
                    </el-button>
                </div>
            </div>
        </div>
    </div>
</template>

<script setup>
import { reactive } from 'vue'
import { Close } from '@element-plus/icons-vue'

const emit = defineEmits(['close', 'analyze'])

const formData = reactive({
    jetDiameter: '0.15',
    pipelinePressure: '3.5',
    leakShape: 'circular',
    leakSize: '0.15',
    leakArea: '0.08',
    gasTemperature: '200',
    environmentalTemperature: '20'
});

const handleAnalyze = () => {
    // 将表单数据传递给父组件
    emit('analyze', formData)
};
</script>

<style lang="scss" scoped>
.open-space-diffusion-model {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 500px;
  display: flex;
  flex-direction: column;
  border-radius: 8px;
  pointer-events: all;

  &::before {
    content: "";
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    backdrop-filter: blur(10px);
    background: rgba(11, 30, 65, 0.8);
    border-radius: 8px;
    border: 1px solid #19385c;
    z-index: -1;
  }

  .analysis-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    border-top-left-radius: 12px;
    padding: 10px 20px;
    font-size: 16px;
    font-weight: bold;
    color: #ffffff;
    border-bottom: 1px solid #19385c;

    .title {
      font-size: 16px;
      font-weight: bold;
      color: #ffffff;
    }

    .close-btn {
      font-size: 22px;
      cursor: pointer;
      color: #ffffff;

      &:hover {
        opacity: 0.8;
      }
    }
  }

  .content-area {
    flex: 1;
    padding: 10px 20px;
    overflow: auto;

    .form-container {
      .form-row {
        display: flex;
        gap: 10px;
        margin-bottom: 15px;

          .form-group {
           display: flex;
           flex-direction: row;
           align-items: center;
           gap: 10px;
           width: 100%;

           .form-label {
             color: rgba(255, 255, 255, 0.9);
             font-size: 12px;
             font-weight: 500;
             width: 180px;
             text-align: right;
             white-space: nowrap;
           }

           .form-input {
              width: 230px;
              height: 32px;
              :deep(.el-input__wrapper) {
                background: rgba(255, 255, 255, 0.1);
                border: 1px solid rgba(255, 255, 255, 0.2);
                border-radius: 4px;
                box-shadow: none;

                &:hover {
                  border-color: rgba(255, 255, 255, 0.4);
                }

                &.is-focus {
                  border-color: #409eff;
                  box-shadow: 0 0 0 1px rgba(64, 158, 255, 0.2);
                }
              }

              :deep(.el-input__inner) {
                color: #ffffff;
                font-size: 12px;

                &::placeholder {
                  color: rgba(255, 255, 255, 0.5);
                }
              }
            }

            .radio-group {
              display: flex;
              gap: 15px;
              align-items: center;

              :deep(.el-radio) {
                margin-right: 0;

                .el-radio__label {
                  color: rgba(255, 255, 255, 0.9);
                  font-size: 12px;
                }

                .el-radio__input {
                  .el-radio__inner {
                    background: rgba(255, 255, 255, 0.1);
                    border: 1px solid rgba(255, 255, 255, 0.2);

                    &:hover {
                      border-color: rgba(255, 255, 255, 0.4);
                    }
                  }

                  &.is-checked {
                    .el-radio__inner {
                      background: #409eff;
                      border-color: #409eff;
                    }
                  }
                }
              }

              :deep(.el-radio-group) {
                display: flex;
                gap: 15px;
                align-items: center;
              }
            }
        }
      }

      .form-actions {
        display: flex;
        justify-content: center;
        margin-top: 20px;

        .analyze-btn {
          background: linear-gradient(135deg, #409eff 0%, #1890ff 100%);
          border: none;
          border-radius: 6px;
          padding: 16px 24px;
          font-size: 14px;
          font-weight: 500;
          color: #ffffff;
          cursor: pointer;
          transition: all 0.3s ease;

          &:hover {
            background: linear-gradient(135deg, #66b1ff 0%, #40a9ff 100%);
            transform: translateY(-1px);
            box-shadow: 0 4px 12px rgba(64, 158, 255, 0.3);
          }

          &:active {
            transform: translateY(0);
          }
        }
      }
    }
  }
}

:deep(.el-select-dropdown) {
  background: rgba(11, 30, 65, 0.95);
  border: 1px solid #19385c;
  backdrop-filter: blur(10px);

  .el-select-dropdown__item {
    color: rgba(255, 255, 255, 0.9);
    font-size: 12px;

    &:hover {
      background: rgba(255, 255, 255, 0.1);
    }

    &.selected {
      background: #409eff;
      color: #ffffff;
    }
  }
}
</style>