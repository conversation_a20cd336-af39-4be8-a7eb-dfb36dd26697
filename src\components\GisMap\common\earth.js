// import * as Cesium from "cesium";

import PrimitiveCluster from "@/components/GisMap/common/PrimitiveCluster.js";
import proj4 from "proj4";
import {
    checkCoordinate,
    cartesian3ToDegree,
} from "@/components/GisMap/common/gisUtil.js";
import {imageInfo} from "@/components/GisMap/images/imageInfo.js";
import {devicesAlarmAll, videoMapAll, videoMapMisAll} from "@/components/GisMap/common/gisInfo.js";
import {mapStates} from "@/components/GisMap/mapStates.js";

export const position0 = [115.097, 35.288, 8000];

// 定义4526坐标系，可以在epsg.io上查
proj4.defs("EPSG:4490", "+proj=+proj=longlat +ellps=GRS80 +no_defs");

export const resize = (res) => {
    const width = window.screen.width;
    const ratio = width / 1920;
    return Math.round(res * ratio);
};

//定义参数start
const tiandituToken = "40a623cbe2c72e883a0c0066ef20e8cd"; //天地图许可
const customTileColor = "#0E3674"
const lineFlow = false; //流动线材质开关

// 创建自定义着色器

export class Earth {
    //****本构造类可按照项目需求进行配置
    constructor(viewer) {
        //在Earth中创建的entity图层，感觉是异步的。_dataSources 是 array(0)
        this.basemap = new BaseMap(viewer);
        this.camera = new Camera(viewer);
        this.entity = new Entity(viewer);
        this.primitive = new Primitive(viewer);
        this.event = new Event(viewer);
        this.listen = new Listen(viewer);
        this.sceneMode = new SceneMode(viewer);
        this.time = new Time(viewer);
        this.roam = new Roam(viewer);
        this.tiles = new Tiles(viewer);
    }
}

class BaseMap {
    constructor(viewer) {
        this.img = undefined;
        this.vec = undefined;
        this.cia = undefined;
        this.osm = undefined;
        this.customImageLayer = undefined;
        this.google = undefined;
        this.mapbox = undefined;
        this.tdt_vec = undefined;
        this.tdt_cva = undefined;
        this.tdt_img = undefined;
        this.tdt_cva = undefined;
        this.tdt_terrain = undefined;
        this.osgbLayer = undefined;
        this.terrainLayer = undefined;

        this.addCustomImageLayer = (url) => {
            this.customImageLayer = viewer.scene.imageryLayers.addImageryProvider(
                new Cesium.UrlTemplateImageryProvider({
                    url: url,
                    tilingScheme: new Cesium.WebMercatorTilingScheme(),
                    fileExtension: "webp",
                    minimumLevel: 0,
                    maximumLevel: 21,
                    rectangle: Cesium.Rectangle.fromDegrees(
                        115.0503730773926065,
                        35.2571118988468371,
                        115.1589521329324981,
                        35.3307943515740845
                    ), //影像的实际范围，仅对该范围内的瓦片发送请求
                })
            );
        }

        this.add = (id) => {
            if (id === "mapbox") {
                if (this.mapbox) {
                    this.mapbox.show = true;
                } else {
                    this.mapbox = viewer.scene.imageryLayers.addImageryProvider(
                        new Cesium.MapboxStyleImageryProvider({
                            url: "https://api.mapbox.com/styles/v1",
                            username: "xxf",
                            styleId: "clft4m7ik000n01mucjtabuzb",
                            accessToken:
                                "pk.eyJ1IjoicmV4bG9uZyIsImEiOiJjbGZ0MnRubDcwYzNsM2ZvMWhteDhrOHlzIn0.XnPgfzDOq_97EHRCxnH5nQ",
                            tilesize: 256,
                            scaleFactor: true,
                        })
                    );
                }
            } else if (id === "vec") {
                if (this.vec) {
                    this.vec.show = true;
                } else {
                    this.vec = viewer.scene.imageryLayers.addImageryProvider(
                        new Cesium.WebMapTileServiceImageryProvider({
                            url: "http://t0.tianditu.gov.cn/vec_w/wmts?tk=" + tiandituToken,
                            layer: "vec",
                            style: "default",
                            tileMatrixSetID: "w",
                            format: "tiles",
                            maximumLevel: 18,
                        })
                    );
                }
            } else if (id === "cva") {
                if (this.cva) {
                    this.cva.show = true;
                } else {
                    this.cva = viewer.scene.imageryLayers.addImageryProvider(
                        new Cesium.WebMapTileServiceImageryProvider({
                            url: "http://t1.tianditu.gov.cn/cva_w/wmts?tk=" + tiandituToken,
                            layer: "cva",
                            style: "default",
                            tileMatrixSetID: "w",
                            format: "tiles",
                            maximumLevel: 18,
                        })
                    );
                }
            } else if (id === "img") {
                if (this.img) {
                    this.img.show = true;
                } else {
                    this.img = viewer.scene.imageryLayers.addImageryProvider(
                        new Cesium.WebMapTileServiceImageryProvider({
                            url: "http://t0.tianditu.gov.cn/img_w/wmts?tk=" + tiandituToken,
                            layer: "img",
                            style: "default",
                            tileMatrixSetID: "w",
                            format: "tiles",
                            maximumLevel: 18,
                        })
                    );
                }
            } else if (id === "cia") {
                if (this.cia) {
                    this.cia.show = true;
                } else {
                    this.cia = viewer.scene.imageryLayers.addImageryProvider(
                        new Cesium.WebMapTileServiceImageryProvider({
                            url: "http://t0.tianditu.gov.cn/cia_w/wmts?tk=" + tiandituToken,
                            layer: "cia",
                            style: "default",
                            tileMatrixSetID: "w",
                            format: "tiles",
                            maximumLevel: 18,
                        })
                    );
                }
            } else if (id === "tdt_vec") {
                if (this.tdt_vec) {
                    this.tdt_vec.show = true;
                } else {
                    this.tdt_vec = viewer.imageryLayers.addImageryProvider(
                        Cesium.TianDiTuImageryProvider(
                            Cesium.TiandituMapsStyle.TDT_VEC_W,
                            tiandituToken,
                            18
                        )
                    );
                }
            } else if (id === "tdt_cva") {
                if (this.tdt_cva) {
                    this.tdt_cva.show = true;
                } else {
                    this.tdt_cva = viewer.imageryLayers.addImageryProvider(
                        Cesium.TianDiTuImageryProvider(
                            Cesium.TiandituMapsStyle.TDT_CVA_W,
                            tiandituToken,
                            18
                        )
                    );
                }
            } else if (id === "tdt_img") {
                if (this.tdt_img) {
                    this.tdt_img.show = true;
                } else {
                    this.tdt_img = viewer.imageryLayers.addImageryProvider(
                        Cesium.TianDiTuImageryProvider(
                            Cesium.TiandituMapsStyle.TDT_IMG_W,
                            tiandituToken,
                            18
                        )
                    );
                }
            } else if (id === "tdt_cia") {
                if (this.tdt_cia) {
                    this.tdt_cia.show = true;
                } else {
                    this.tdt_cia = viewer.imageryLayers.addImageryProvider(
                        Cesium.TianDiTuImageryProvider(
                            Cesium.TiandituMapsStyle.TDT_CIA_W,
                            tiandituToken,
                            18
                        )
                    );
                }
            } else if (id === "tdt_terrain") {
               if (this.tdt_terrain) {
                   this.tdt_terrain.show = true;
               } else {
                   const randomTokens = [tiandituToken]
                   this.tdt_terrain = viewer.terrainProvider = new Cesium.TiandituTerrainProvider({
                       token: tiandituToken,
                       customTags: {
                           token: function (terrainProvider, x, y, level) {
                               return randomTokens[(x + y + level) % randomTokens.length];
                           },
                       },
                   });
               }
            }
        };

        this.addTerrain = (url) => {
            if (this.terrainLayer) {
                this.terrainLayer.show = true;
            } else {
                this.terrainLayer = new Cesium.CesiumTerrainProvider({
                    url: url,
                    name: "DEM",
                });
                viewer.terrainProvider = this.terrainLayer;
            }
        };

        this.addOsgbModel = (url) => {
            if (this.osgbLayer) {
                this.osgbLayer.show = true;
            } else {
                //添加倾斜摄影模型
                this.osgbLayer = new Cesium.Layer({
                    name: "倾斜摄影",
                    url: url,
                    // lightColor: new Cesium.Cartesian3(200.0, 200.0, 200.0),
                    skipLevelOfDetail: true, //添加此句话可以提高加载倾斜摄影数据的速度
                });
                viewer.scene.layers.add(this.osgbLayer);
            }
        }

        this.addPipeLineModel = (id, url) => {
            const layer = viewer.scene.layers.find(id);
            if (layer) {
                layer.show = true;
                return layer;
            } else {
                let lf = 30;
                //添加管道模型
                const layer = new Cesium.Layer({
                    name: id,
                    url: url,
                    lightColor: new Cesium.Cartesian3(lf, lf, lf),
                    show: true,
                });
                viewer.scene.layers.add(layer);
                return layer;
            }
        }

        this.addBridgeModel = (id, url) => {
            const layer = viewer.scene.layers.find(id);
            if (layer) {
                layer.show = true;
                return layer;
            } else {
                const layer = new Cesium.Layer({
                    name: id,
                    url: url,
                    // geomErrorScale: 0.5, //模型缩放比例
                    skipLevelOfDetail: true, //添加此句话可以提高加载倾斜摄影数据的速度
                });
                viewer.scene.layers.add(layer);
                return layer;
            }
        }

        this.removeModelLayerByNames = (ids) => {
            if (!ids) return;
            for (let i = 0; i < ids.length; i++) {
                const id = ids[i];
                const modelLayer = viewer.scene.layers.find(id);
                if (modelLayer) {
                    viewer.scene.layers.remove(modelLayer);
                }
            }
        }

        this.showModelLayerByNames = (names, show = true) => {
            if (Array.isArray(names)) {
                names.forEach(name => {
                    const layer = viewer.scene.layers.find(name);
                    if (layer) {
                        layer.show = show;
                    }
                });
            } else {
                const layer = viewer.scene.layers.find(names);
                if (layer) {
                    layer.show = show;
                }
            }
        };

        this.addSuperMapWMTS = (options) => {
            viewer.scene.imageryLayers.addImageryProvider(
                new Cesium.WebMapTileServiceImageryProvider({
                    url: options.url, //'https://iserver.supermap.io/iserver/services/map-china400/wmts-china', //geoserver服务地址，如：'http://localhost:8080/geoserver/gwc/service/wmts'
                    layer: options.layer, //图层名称，如：'China'
                    style: "default",
                    format: "image/png",
                    tileMatrixSetID: options.tileMatrixSetID, //'GoogleMapsCompatible_China',
                    tilingScheme: new Cesium.WebMercatorTilingScheme(), //墨卡托投影坐标系 Cesium.WebMercatorTilingScheme
                    maximumLevel: 19,
                })
            );
        };

        // @ts-ignore
        this.getUrlParam = (url) => {
            // str为？之后的参数部分字符串
            const str = url.substr(url.indexOf("?") + 1);
            // arr每个元素都是完整的参数键值
            const arr = str.split("&");
            // result为存储参数键值的集合
            const result = {};
            for (let i = 0; i < arr.length; i++) {
                // item的两个元素分别为参数名和参数值
                const item = arr[i].split("=");
                result[item[0]] = item[1];
            }
            return result;
        };

        //传参示例：超图发布的wms服务：https://iserver.supermap.io/iserver/services/map-china400/wms111/China?layers=China&srs=EPSG:4326
        //传参示例：geoserver发布的wms服务：http://localhost:8080/geoserver/tiger/wms?layers=tiger:poly_landmarks&srs=EPSG:4326
        this.addWMSLayer = (options) => {
            // 参数处理
            // 参数处理
            const mapUrl = options.url.substr(0, options.url.indexOf("?"));
            const mapParameters = {
                url: mapUrl,
                parameters: {
                    transparent: true,
                    format: "image/png",
                },
            };
            const params = this.getUrlParam(options.url);
            Object.keys(params).forEach((key) => {
                if (params[key]) {
                    mapParameters.parameters[key] = params[key];
                }
            });
            mapParameters.layers = mapParameters.parameters.layers;
            // 初始化图层
            viewer.scene.imageryLayers.addImageryProvider(
                new Cesium.WebMapServiceImageryProvider(mapParameters)
            );
        };

        //传参示例---超图发布的wmts服务：https://iserver.supermap.io/iserver/services/map-china400/wmts-china?layer=China&tileMatrixSetID=ChinaPublicServices_China&style=default
        //传参示例---geoserver发布的wmts服务：http://localhost:8080/geoserver/gwc/service/wmts?layer=sf:restricted&tileMatrixSetID=EPSG:4326&style=default
        this.addWMTSLayer = (options) => {
            // 参数处理
            const mapUrl = options.url.substr(0, options.url.indexOf("?"));
            const mapParameters = null;
            const params = this.getUrlParam(options.url);
            Object.keys(params).forEach((key) => {
                if (params[key]) {
                    mapParameters[key] = params[key];
                }
            });
            //切片方案选择：3857使用WebMercatorTilingScheme，地理坐标系使用GeographicTilingScheme
            if (options.id.includes("3857")) {
                mapParameters.tilingScheme = new Cesium.WebMercatorTilingScheme();
            } else if (options.id.includes("4490")) {
                /* mapParameters.tileMatrixLabels = [
                  "1",
                  "2",
                  "3",
                  "4",
                  "5",
                  "6",
                  "7",
                  "8",
                  "9",
                  "10",
                  "11",
                  "12",
                  "13",
                  "14",
                  "15",
                  "16",
                  "17",
                  "18",
                  "19",
                ];*/
                mapParameters.tilingScheme = new Cesium.GeographicTilingScheme({
                    /*   rectangle: new Cesium.Rectangle(
                      Cesium.Math.toRadians(119.45046424900006),
                      Cesium.Math.toRadians(32.575532913000075),
                      Cesium.Math.toRadians(120.96887779200006),
                      Cesium.Math.toRadians(34.46629524200017)
                    ),*/
                    numberOfLevelZeroTilesX: 2,
                    numberOfLevelZeroTilesY: 1,
                });
                /* mapParameters.tilingScheme = new GISTilingScheme();
                mapParameters.maximumLevel = 20;
                mapParameters.rectangle = new Cesium.Rectangle(
                  Cesium.Math.toRadians(119.45046424900006),
                  Cesium.Math.toRadians(32.575532913000075),
                  Cesium.Math.toRadians(120.96887779200006),
                  Cesium.Math.toRadians(34.46629524200017)
                );*/
            } else {
                mapParameters.tilingScheme = new Cesium.GeographicTilingScheme();
            }
            // 初始化图层
            viewer.scene.imageryLayers.addImageryProvider(
                new Cesium.WebMapTileServiceImageryProvider(mapParameters)
            );
        };

        this.raise = (map) => {
            viewer.scene.imageryLayers.raiseToTop(map);
        };
    }
}

class Camera {
    constructor(viewer) {
        this.flyTo = ({
                          lon,
                          lat,
                          height = 1000,
                          orientation = {heading: 0, pitch: -45, roll: 0},
                          duration = 1,
                      }) => {
            // const offsetY = (height * 0.015) / 1800;
            viewer.camera.flyTo({
                // destination: Cesium.Cartesian3.fromDegrees(lon, lat - offsetY, height),
                destination: Cesium.Cartesian3.fromDegrees(lon, lat, height),
                orientation: {
                    heading: Cesium.Math.toRadians(orientation.heading),
                    pitch: Cesium.Math.toRadians(orientation.pitch),
                    roll: Cesium.Math.toRadians(orientation.roll),
                },
                duration: duration,
            });
        };

        this.flyToBoundingSphere = ({
                                        position,
                                        distanceMode = "current",
                                        distanceFixed = 500,
                                        distanceScale = 1.7,
                                    }) => {
            const camera = viewer.scene.camera;
            const cartesian3 = camera.position;
            const Scene3D = viewer.scene.mode === Cesium.SceneMode.SCENE3D;
            const height = Cesium.Cartographic.fromCartesian(cartesian3).height;
            const height2D = viewer.scene.camera.getMagnitude();
            const distance = Math.abs(height / Math.sin(camera.pitch));
            let rangeDistance, deltX, deltY;

            if (distanceMode === "current") {
                rangeDistance = Scene3D ? distance : height2D;
                if (Scene3D) {
                    deltX = Math.sin(camera.heading) * height * 0.000001;
                    deltY = Math.cos(camera.heading) * height * 0.000001;
                } else {
                    deltX = Math.sin(camera.heading) * height2D * 0.000001;
                    deltY = Math.cos(camera.heading) * height2D * 0.000001;
                }
            } else if (distanceMode === "fixed") {
                rangeDistance = distanceFixed;
                deltX = 0;
                deltY = 0;
            } else {
                rangeDistance = (Scene3D ? distance : height) / distanceScale;
                deltX = 0;
                deltY = 0;
            }

            const bs = new Cesium.BoundingSphere(
                Cesium.Cartesian3.fromDegrees(
                    position[0] + deltX,
                    position[1] + deltY,
                    0
                ),
                100
            );

            const offset = new Cesium.HeadingPitchRange(
                Scene3D ? camera.heading : Cesium.Math.toRadians(0),
                Scene3D ? camera.pitch : Cesium.Math.toRadians(-90),
                rangeDistance
            );
            camera.flyToBoundingSphere(bs, {offset, duration: 1});
        };

        this.setView = ({
                            lon,
                            lat,
                            height,
                            orientation = {heading: 0, pitch: -90, roll: 0},
                        }) => {
            if (checkCoordinate(lon, lat)) {
                viewer.camera.setView({
                    destination: Cesium.Cartesian3.fromDegrees(lon, lat, height),
                    orientation: {
                        heading: Cesium.Math.toRadians(orientation.heading),
                        pitch: Cesium.Math.toRadians(orientation.pitch),
                        roll: Cesium.Math.toRadians(orientation.roll),
                    },
                });
            } else {
                // console.log("错误提示:camera.setView经纬度错误");
            }
        };

        this.getCameraInfo = (print = false) => {
            const cartesian3 = viewer.camera.position;
            const cartographic = Cesium.Cartographic.fromCartesian(cartesian3);
            const lng = Cesium.Math.toDegrees(cartographic.longitude);
            const lat = Cesium.Math.toDegrees(cartographic.latitude);
            const position = [lng, lat];
            const direction = viewer.camera.direction;
            const transform = viewer.camera.transform;
            const cameraFlytoParam = {
                lon: lng,
                lat: lat,
                height: cartographic.height,
                orientation: {
                    heading: Cesium.Math.toDegrees(viewer.camera.heading),
                    pitch: Cesium.Math.toDegrees(viewer.camera.pitch),
                    roll: Cesium.Math.toDegrees(viewer.camera.roll),
                },
                duration: 1,
            };
            if (print) {
                // console.log("错误提示:getCameraInfo", cameraFlytoParam);
            }
            return cameraFlytoParam;
        };

        this.getCameraHeight = () => {
            const cartesian3 = viewer.camera.position;
            const cartographic = Cesium.Cartographic.fromCartesian(cartesian3);
            return cartographic.height;
        };

        this.onMapZoomIn = () => {
            const position = viewer.camera.positionCartographic;
            const height = position.height;
            viewer.camera.zoomIn(height / 10);
        };

        this.onMapZoomOut = () => {
            const position = viewer.camera.positionCartographic;
            const height = position.height;
            viewer.camera.zoomOut(height / 10);
        };
    }
}

class Entity {
    constructor(viewer) {
        this.cluserCanvasImages = {};
        this.customCanvasImages = {};
        this.highlightDataSource = new Cesium.CustomDataSource('highlightLayer');
        // 确保高亮数据源始终可见且在最顶层
        this.highlightDataSource.show = true;
        viewer.dataSources.add(this.highlightDataSource);
        // 将高亮数据源移到最顶层
        viewer.dataSources.raiseToTop(this.highlightDataSource);
        this.lastHighlightedEntity = undefined;

        // @ts-ignore
        this.addPointGeometryFromDegrees = ({
                                                layerId,
                                                data,
                                                width = 53,
                                                height = 36,
                                                show = true,
                                                isCluster = true,
                                                showLabel = false,
                                            }) => {
            // 清除指定图层下的所有数据源实体;
            this.clearDataSourcesEntitiesByLayerId(layerId);

            const findDataSource = viewer.dataSources.getByName(layerId);
            if (findDataSource.length <= 0) {
                findDataSource[0] = new Cesium.CustomDataSource(layerId);
                viewer.dataSources.add(findDataSource[0]);
            }
            findDataSource[0].show = show;

            // 优化后的动态计算偏移量函数
            const calculateDynamicOffset = () => {
                // 根据地图模式获取正确的相机高度
                let cameraHeight;
                if (viewer.scene.mode === Cesium.SceneMode.SCENE2D) {
                    cameraHeight = viewer.camera.getMagnitude();
                } else {
                    cameraHeight = viewer.camera.positionCartographic.height;
                }

                // 计算图标高度（考虑缩放因素）
                const iconHeight = resize(height);
                let iconScale = 1.0;

                // 根据相机高度计算图标缩放比例
                if (viewer.scene.mode === Cesium.SceneMode.SCENE2D) {
                    // 2D模式下使用更保守的缩放
                    if (cameraHeight < 1000) {
                        iconScale = 1.0;
                    } else if (cameraHeight < 5000) {
                        iconScale = 0.8;
                    } else {
                        iconScale = 0.6;
                    }
                } else {
                    // 3D模式下的缩放计算
                    if (cameraHeight < 500) {
                        iconScale = 1.5;
                    } else if (cameraHeight < 1000) {
                        iconScale = 1.2;
                    } else if (cameraHeight < 4000) {
                        iconScale = 1.0;
                    } else if (cameraHeight < 8000) {
                        iconScale = 0.8;
                    } else if (cameraHeight < 14000) {
                        iconScale = 0.6;
                    } else {
                        iconScale = 0.4;
                    }
                }

                // 计算实际图标高度
                const actualIconHeight = iconHeight * iconScale;

                // 计算标签高度（考虑字体大小）
                const fontSize = calculateDynamicFontSize();
                const labelHeight = fontSize * 1.2; // 字体高度的估算

                // 计算标签背景高度（包括padding）
                const backgroundPadding = 10;
                const totalLabelHeight = labelHeight + (backgroundPadding * 2);

                // 优化偏移量计算：确保标签始终在图标正上方，间距稳定
                // fixedSpacing根据相机高度动态调整，避免重叠
                let fixedSpacing;
                if (cameraHeight < 1000) {
                    fixedSpacing = resize(20); // 近距离，间距大
                } else if (cameraHeight < 5000) {
                    fixedSpacing = resize(14); // 中距离，间距适中
                } else {
                    fixedSpacing = resize(8); // 远距离，间距小
                }
                // 计算偏移量：图标高度 + 固定间距
                // 由于Cesium坐标系统Y轴向下为正，所以返回负值
                const offsetY = actualIconHeight + fixedSpacing;

                return -offsetY;
            };

            // 动态计算billboard偏移量的函数
            const calculateDynamicPixelOffset = () => {
                // 根据地图模式获取正确的相机高度
                let cameraHeight;
                if (viewer.scene.mode === Cesium.SceneMode.SCENE2D) {
                    cameraHeight = viewer.camera.getMagnitude();
                } else {
                    cameraHeight = viewer.camera.positionCartographic.height;
                }

                // console.log(`当前相机高度: ${cameraHeight}`);

                let offsetY;
                if (cameraHeight < 500) {
                    offsetY = -resize(height * 6.0);
                } else if (cameraHeight < 1000) {
                    offsetY = -resize(height * 3.0);
                } else if (cameraHeight < 2000) {
                    offsetY = -resize(height * 2.0);
                }  else if (cameraHeight < 4000) {
                    offsetY = -resize(height * 1.5);
                }  else if (cameraHeight < 8000) {
                    offsetY = -resize(height * 1.0);
                } else if (cameraHeight < 14000) {
                    offsetY = -resize(height * 0.8);
                } else {
                    offsetY = -resize(height * 0.4);
                }

                return new Cesium.Cartesian2(0, offsetY);
            };

            // 优化后的动态计算字体大小函数
            const calculateDynamicFontSize = () => {
                // 根据地图模式获取正确的相机高度
                let cameraHeight;
                if (viewer.scene.mode === Cesium.SceneMode.SCENE2D) {
                    cameraHeight = viewer.camera.getMagnitude();
                } else {
                    cameraHeight = viewer.camera.positionCartographic.height;
                }

                let fontSize;
                if (cameraHeight < 500) {
                    fontSize = resize(16);
                } else if (cameraHeight < 2000) {
                    fontSize = resize(14);
                } else if (cameraHeight < 10000) {
                    fontSize = resize(12);
                } else {
                    fontSize = resize(10);
                }
                return fontSize;
            };

            data.map((item) => {
                const entity = findDataSource[0].entities.add({
                    id: item.id,
                    name: layerId,
                    show: true,
                    position: Cesium.Cartesian3.fromDegrees(
                        parseFloat(item.longitude),
                        parseFloat(item.latitude)
                    ),
                    billboard: {
                        image: imageInfo[item.gisType],
                        width: resize(width),
                        height: resize(height),
                        verticalOrigin: Cesium.VerticalOrigin.BOTTOM,
                        // 在2D模式下，完全移除缩放和距离限制，确保始终可见
                        ...(viewer.scene.mode === Cesium.SceneMode.SCENE2D ? {
                            // 2D模式：不使用scaleByDistance和distanceDisplayCondition
                            disableDepthTestDistance: Number.POSITIVE_INFINITY,
                            heightReference: Cesium.HeightReference.RELATIVE_TO_GROUND,
                            show: true,
                        } : {
                            // 3D模式：保持原有参数
                            scaleByDistance: new Cesium.NearFarScalar(100, 1.5, 2e4, 0.3),
                            disableDepthTestDistance: Number.POSITIVE_INFINITY,
                            heightReference: Cesium.HeightReference.RELATIVE_TO_GROUND,
                            translucencyByDistance: new Cesium.NearFarScalar(
                                1.5e3,
                                1.0,
                                1.5e7,
                                0.2
                            ),
                            distanceDisplayCondition: new Cesium.DistanceDisplayCondition(0, 1000000),
                            show: true,
                        }),
                    },
                    label: showLabel ? {
                        text: item.name || '',
                        font: new Cesium.CallbackProperty(() => {
                            return `bold ${calculateDynamicFontSize()}px MicroSoft YaHei`;
                        }, false),
                        verticalOrigin: Cesium.VerticalOrigin.BOTTOM, // 标签底部对齐
                        horizontalOrigin: Cesium.HorizontalOrigin.CENTER, // 确保水平居中
                        fillColor: Cesium.Color.fromCssColorString('#3880ea').withAlpha(1),
                        outlineColor: Cesium.Color.BLACK,
                        outlineWidth: 2,
                        style: Cesium.LabelStyle.FILL,
                        pixelOffset: new Cesium.CallbackProperty(() => {
                            return calculateDynamicPixelOffset();
                        }, false),
                        disableDepthTestDistance: Number.POSITIVE_INFINITY,
                        showBackground: true,
                        backgroundColor: Cesium.Color.BLACK.withAlpha(0.5),
                        backgroundPadding: new Cesium.Cartesian2(10, 5),
                        showBorder: true,
                        borderColor: Cesium.Color.fromCssColorString('#3880ea').withAlpha(1),
                        borderWidth: 2,
                        distanceDisplayCondition: new Cesium.DistanceDisplayCondition(0, 14000),
                        // 移除标签的缩放控制，保持固定大小，避免间距变化
                        // scaleByDistance: new Cesium.NearFarScalar(500, 1.0, 5000, 0.8),
                    } : undefined,
                    properties: {
                        tag: item,
                    }
                });
            });

            // 动态控制聚合效果
            const updateClustering = () => {
                // 根据地图模式获取正确的相机高度
                let cameraHeight;
                if (viewer.scene.mode === Cesium.SceneMode.SCENE2D) {
                    // 二维模式下使用相机的magnitude作为高度
                    cameraHeight = viewer.camera.getMagnitude();
                } else {
                    // 三维模式下使用positionCartographic.height
                    cameraHeight = viewer.camera.positionCartographic.height;
                }

                // 在二维模式下使用10000作为聚合阈值，在三维模式下使用500作为聚合阈值
                const shouldCluster = isCluster && cameraHeight >= (viewer.scene.mode === Cesium.SceneMode.SCENE2D ? 5000 : 500);

                findDataSource[0].clustering.enabled = shouldCluster;

                if (shouldCluster) {
                    // 在二维模式下使用更小的像素范围，三维模式下使用较大的像素范围
                    findDataSource[0].clustering.pixelRange = viewer.scene.mode === Cesium.SceneMode.SCENE2D ? 30 : 50;
                    findDataSource[0].clustering.minimumClusterSize = 2;

                    // 移除之前的事件监听器（如果存在）
                    if (findDataSource[0].clustering.clusterEvent.numberOfListeners > 0) {
                        findDataSource[0].clustering.clusterEvent.removeEventListener(
                            findDataSource[0]._clusterEventListener
                        );
                    }

                    // 添加新的事件监听器
                    const clusterEventListener = (clusteredEntities, cluster) => {
                        // 关闭自带的显示聚合数量的标签
                        cluster.label.show = false;
                        cluster.billboard.show = true;
                        cluster.billboard.verticalOrigin = Cesium.VerticalOrigin.BOTTOM;
                        cluster.billboard.disableDepthTestDistance = Number.POSITIVE_INFINITY;
                        // 在二维模式下，确保聚合图标有合适的显示条件
                        if (viewer.scene.mode === Cesium.SceneMode.SCENE2D) {
                            // 2D模式：移除所有缩放和距离限制，只设置一次
                            if (cluster.billboard.scaleByDistance !== undefined) {
                                cluster.billboard.scaleByDistance = undefined;
                            }
                            if (cluster.billboard.distanceDisplayCondition !== undefined) {
                                cluster.billboard.distanceDisplayCondition = undefined;
                            }
                            if (cluster.billboard.translucencyByDistance !== undefined) {
                                cluster.billboard.translucencyByDistance = undefined;
                            }
                            if (cluster.billboard.disableDepthTestDistance !== Number.POSITIVE_INFINITY) {
                                cluster.billboard.disableDepthTestDistance = Number.POSITIVE_INFINITY;
                            }
                            if (cluster.billboard.heightReference !== Cesium.HeightReference.NONE) {
                                cluster.billboard.heightReference = Cesium.HeightReference.NONE;
                            }
                            cluster.billboard.show = true;
                        }

                        // 检查是否有报警状态的实体
                        let hasAlarm = false;
                        let hasOffline = false;
                        for (let i = 0; i < clusteredEntities.length; i++) {
                            const entity = clusteredEntities[i];
                            if (devicesAlarmAll.includes(entity.name)) {
                                if (entity.properties &&
                                    entity.properties.tag &&
                                    entity.properties.tag._value?.alarmStatus &&
                                    entity.properties.tag._value?.alarmStatus !== "0") {
                                    hasAlarm = true; // 有报警状态的实体聚合图标
                                    break;
                                } else if (entity.properties &&
                                    entity.properties.tag &&
                                    entity.properties.tag._value?.onlineStatus &&
                                    entity.properties.tag._value?.onlineStatus === "0") {
                                    hasOffline = true; // 有离线状态的实体聚合图标
                                }
                            } else if (videoMapAll.includes(entity.name) || videoMapMisAll.includes(entity.name)) {
                                if (entity.properties &&
                                    entity.properties.tag &&
                                    !entity.properties.tag._value?.online) {
                                    hasOffline = true; // 视频监控离线状态的实体聚合图标
                                    break;
                                }
                            } else {
                                break; // 如果不是设备或视频监控类型的实体，显示默认蓝色聚合图标
                            }
                        }

                        cluster.billboard.image = this.getCluserCanvasImage(clusteredEntities.length, hasAlarm, hasOffline);
                    };

                    findDataSource[0].clustering.clusterEvent.addEventListener(clusterEventListener);
                    // 保存监听器引用以便后续移除
                    findDataSource[0]._clusterEventListener = clusterEventListener;
                }
            };

            // 初始设置聚合状态
            updateClustering();
            // 点位加载完成强制刷新渲染
            viewer.scene.requestRender();

            // 强制渲染数据源
            this.forceRenderDataSource(findDataSource[0]);

            // 在2D模式下，额外确保渲染
            this.ensure2DRendering(findDataSource[0]);

            // 监听相机移动事件，动态更新聚合状态和标签位置
            const cameraChangeHandler = () => {
                const oldClusterEnabled = findDataSource[0].clustering.enabled;
                updateClustering();

                // 只在聚合状态发生变化时才强制渲染
                if (oldClusterEnabled !== findDataSource[0].clustering.enabled) {
                    viewer.scene.requestRender();
                }
            };

            // 移除之前的相机事件监听器（如果存在）
            if (findDataSource[0]._cameraChangeHandler) {
                viewer.camera.changed.removeEventListener(findDataSource[0]._cameraChangeHandler);
            }

            // 添加相机变化监听器
            viewer.camera.changed.addEventListener(cameraChangeHandler);
            // 保存监听器引用以便后续移除
            findDataSource[0]._cameraChangeHandler = cameraChangeHandler;
        };

        this.addPointGeometryCustomRender = ({
                                                 layerId,
                                                 data,
                                                 width = 50,
                                                 height = 50,
                                                 show = true,
                                             }) => {
            // 清除指定图层下的所有数据源实体;
            this.clearDataSourcesEntitiesByLayerId(layerId);

            const findDataSource = viewer.dataSources.getByName(layerId);
            if (findDataSource.length <= 0) {
                findDataSource[0] = new Cesium.CustomDataSource(layerId);
                viewer.dataSources.add(findDataSource[0]);
            }
            findDataSource[0].show = show;

            // 节流函数，限制函数执行频率
            const throttle = (func, delay) => {
                let timeoutId;
                let lastExecTime = 0;
                return function (...args) {
                    const currentTime = Date.now();

                    if (currentTime - lastExecTime > delay) {
                        func.apply(this, args);
                        lastExecTime = currentTime;
                    } else {
                        clearTimeout(timeoutId);
                        timeoutId = setTimeout(() => {
                            func.apply(this, args);
                            lastExecTime = Date.now();
                        }, delay - (currentTime - lastExecTime));
                    }
                };
            };

            // 动态计算billboard偏移量的函数
            const calculateDynamicPixelOffset = () => {
                // 根据地图模式获取正确的相机高度
                let cameraHeight;
                if (viewer.scene.mode === Cesium.SceneMode.SCENE2D) {
                    cameraHeight = viewer.camera.getMagnitude();
                } else {
                    cameraHeight = viewer.camera.positionCartographic.height;
                }

                let offsetY;
                if (cameraHeight < 300) {
                    offsetY = -resize(height * 2.5);
                } else if (cameraHeight < 1000) {
                    offsetY = -resize(height * 2.0);
                } else if (cameraHeight < 4000) {
                    offsetY = -resize(height * 1.3);
                }  else if (cameraHeight < 8000) {
                  offsetY = -resize(height * 1.0);
                } else if (cameraHeight < 14000) {
                    offsetY = -resize(height * 0.8);
                } else {
                    offsetY = -resize(height * 0.4);
                }

                return new Cesium.Cartesian2(0, offsetY);
            };

            // 动态计算billboard缩放的函数
            const calculateDynamicScale = () => {
                // 根据地图模式获取正确的相机高度
                let cameraHeight;
                if (viewer.scene.mode === Cesium.SceneMode.SCENE2D) {
                    cameraHeight = viewer.camera.getMagnitude();
                } else {
                    cameraHeight = viewer.camera.positionCartographic.height;
                }

                let scale;

                if (cameraHeight < 500) {
                    scale = 1.3;
                } else if (cameraHeight < 2000) {
                    scale = 1.1;
                } else if (cameraHeight < 5000) {
                    scale = 1.0;
                } else {
                    scale = 0.9;
                }

                return scale;
            };

            data.map((item) => {
                const entity = findDataSource[0].entities.add({
                    id: item.id + "_style",
                    name: layerId,
                    show: true,
                    position: Cesium.Cartesian3.fromDegrees(
                        parseFloat(item.longitude),
                        parseFloat(item.latitude)
                    ),
                    billboard: {
                        image: item?.score ? this.getCustomCanvasImage(item?.score) : null,
                        width: resize(width),
                        height: resize(height),
                        // 使用CallbackProperty实现动态偏移
                        pixelOffset: new Cesium.CallbackProperty(() => {
                            return calculateDynamicPixelOffset();
                        }, false),
                        // 使用CallbackProperty实现动态缩放
                        scale: new Cesium.CallbackProperty(() => {
                            return calculateDynamicScale();
                        }, false),
                        verticalOrigin: Cesium.VerticalOrigin.BOTTOM,
                        // 在2D模式下，完全移除缩放和距离限制，确保始终可见
                        ...(viewer.scene.mode === Cesium.SceneMode.SCENE2D ? {
                            // 2D模式：不使用scaleByDistance和distanceDisplayCondition
                            disableDepthTestDistance: Number.POSITIVE_INFINITY,
                            heightReference: Cesium.HeightReference.RELATIVE_TO_GROUND,
                        } : {
                            // 3D模式：保持原有参数
                            scaleByDistance: new Cesium.NearFarScalar(100, 1.5, 1e4, 0.5),
                            disableDepthTestDistance: Number.POSITIVE_INFINITY,
                            heightReference: Cesium.HeightReference.RELATIVE_TO_GROUND,
                            translucencyByDistance: new Cesium.NearFarScalar(
                                1.5e3,
                                1.0,
                                1.5e7,
                                0.2
                            ),
                            distanceDisplayCondition: new Cesium.DistanceDisplayCondition(0, 500000),
                        }),
                    },
                    properties: {
                        tag: item,
                    }
                });
            });

            // 优化后的相机变化处理函数，使用节流机制
            const cameraChangeHandler = throttle(() => {
                // 只在需要时请求渲染
                viewer.scene.requestRender();
            }, 200); // 限制为每200ms最多执行一次，减少渲染频率

            // 移除之前的相机事件监听器（如果存在）
            if (findDataSource[0]._customRenderCameraHandler) {
                viewer.camera.changed.removeEventListener(findDataSource[0]._customRenderCameraHandler);
            }

            // 添加相机变化监听器
            viewer.camera.changed.addEventListener(cameraChangeHandler);
            findDataSource[0]._customRenderCameraHandler = cameraChangeHandler;

            // 强制渲染数据源
            this.forceRenderDataSource(findDataSource[0]);
            this.ensure2DRendering(findDataSource[0]);
        };

        this.addCircleGeometryFromDegrees = ({
                                                 layerId,
                                                 position,
                                                 radius = 1000,
                                                 colorStr = "#D9001B",
                                                 show = true,
                                             }) => {
            // 清除指定图层下的所有数据源实体;
            this.clearDataSourcesEntitiesByLayerId(layerId);
            const findDataSource = viewer.dataSources.getByName(layerId);
            if (findDataSource.length <= 0) {
                findDataSource[0] = new Cesium.CustomDataSource(layerId);
                viewer.dataSources.add(findDataSource[0]);
            }
            findDataSource[0].show = show;
            findDataSource[0].entities.add({
                name: layerId,
                show: true,
                position: Cesium.Cartesian3.fromDegrees(position[0], position[1]),
                ellipse: {
                    semiMinorAxis: radius,
                    semiMajorAxis: radius,
                    material: Cesium.Color.fromCssColorString(colorStr).withAlpha(0.8),
                    outline: true,
                    outlineColor: Cesium.Color.fromCssColorString(colorStr).withAlpha(1),
                },
            });
        };

        this.getCenterFromPolylineGeometry = (geometry) => {
            if (!geometry || !geometry.coordinates) {
                return null;
            }

            let allCoordinates = [];

            // 根据几何类型处理坐标
            if (geometry.type === 'LineString') {
                allCoordinates = geometry.coordinates;
            } else if (geometry.type === 'MultiLineString') {
                // 对于 MultiLineString，取第一条线串或合并所有线串
                // 这里选择第一条线串作为主要线串
                if (geometry.coordinates.length > 0) {
                    allCoordinates = geometry.coordinates[0];
                }
            } else {
                console.warn('不支持的几何类型:', geometry.type);
                return null;
            }

            if (allCoordinates.length === 0) {
                return null;
            }

            // 如果只有一个点，直接返回该点
            if (allCoordinates.length === 1) {
                return {
                    longitude: allCoordinates[0][0],
                    latitude: allCoordinates[0][1],
                    height: allCoordinates[0][2] || 0
                };
            }

            // 计算线的总长度
            let totalDistance = 0;
            const distances = [];

            for (let i = 0; i < allCoordinates.length - 1; i++) {
                const coord1 = allCoordinates[i];
                const coord2 = allCoordinates[i + 1];

                // 使用 Cesium 计算两点间的距离
                const cartesian1 = Cesium.Cartesian3.fromDegrees(coord1[0], coord1[1], coord1[2] || 0);
                const cartesian2 = Cesium.Cartesian3.fromDegrees(coord2[0], coord2[1], coord2[2] || 0);
                const distance = Cesium.Cartesian3.distance(cartesian1, cartesian2);

                distances.push(distance);
                totalDistance += distance;
            }

            // 找到中点位置（总长度的一半）
            const halfDistance = totalDistance / 2;
            let accumulatedDistance = 0;

            // 遍历线段，找到包含中点的线段
            for (let i = 0; i < distances.length; i++) {
                const segmentDistance = distances[i];

                if (accumulatedDistance + segmentDistance >= halfDistance) {
                    // 中点在当前线段内
                    const remainingDistance = halfDistance - accumulatedDistance;
                    const ratio = remainingDistance / segmentDistance;

                    const coord1 = allCoordinates[i];
                    const coord2 = allCoordinates[i + 1];

                    // 线性插值计算中点坐标
                    const centerLon = coord1[0] + (coord2[0] - coord1[0]) * ratio;
                    const centerLat = coord1[1] + (coord2[1] - coord1[1]) * ratio;
                    const centerHeight = (coord1[2] || 0) + ((coord2[2] || 0) - (coord1[2] || 0)) * ratio;

                    return {
                        longitude: centerLon,
                        latitude: centerLat,
                        height: centerHeight
                    };
                }

                accumulatedDistance += segmentDistance;
            }

            // 如果没有找到（理论上不应该发生），返回几何中心
            const centerIndex = Math.floor(allCoordinates.length / 2);
            const centerCoord = allCoordinates[centerIndex];

            return {
                longitude: centerCoord[0],
                latitude: centerCoord[1],
                height: centerCoord[2] || 0
            };
        }

        this.addPolylineGeometryFromDegrees = ({
                                                   layerId,
                                                   data,
                                                   material,
                                                   width = 4,
                                                   show = true,
                                               }) => {
            // 清除指定图层下的所有数据源实体;
            this.clearDataSourcesEntitiesByLayerId(layerId);

            const findDataSource = viewer.dataSources.getByName(layerId);
            if (findDataSource.length <= 0) {
                findDataSource[0] = new Cesium.CustomDataSource(layerId);
                viewer.dataSources.add(findDataSource[0]);
            }
            findDataSource[0].show = show;
            data.map((item) => {
                // 将经纬度坐标转换为带高程的坐标数组
                const positions = [];

                // 根据 item.geometry 类型处理坐标数据
                if (item.geometry && item.geometry.coordinates) {
                    const coordinates = item.geometry.coordinates;

                    // 处理不同的几何类型
                    if (item.geometry.type === 'LineString') {
                        // LineString: coordinates 是一个坐标数组 [[lon, lat], [lon, lat], ...]
                        coordinates.forEach(coord => {
                            const lon = coord[0];
                            const lat = coord[1];
                            const existingHeight = coord[2]; // 检查是否已有高度信息

                            let height;
                            if (existingHeight !== undefined && existingHeight !== null) {
                                // 如果坐标中已包含高度信息，使用该高度
                                height = existingHeight;
                            } else {
                                // 获取地形高程
                                const cartographic = Cesium.Cartographic.fromDegrees(lon, lat);
                                height = viewer.scene.globe.getHeight(cartographic) || 0;
                                // 添加一个小的偏移量确保线条显示在地面上
                                height += 2;
                            }

                            positions.push(Cesium.Cartesian3.fromDegrees(lon, lat, height));
                        });
                    } else if (item.geometry.type === 'MultiLineString') {
                        // MultiLineString: coordinates 是一个线串数组 [[[lon, lat], [lon, lat]], [[lon, lat], [lon, lat]]]
                        coordinates.forEach(lineString => {
                            lineString.forEach(coord => {
                                const lon = coord[0];
                                const lat = coord[1];
                                const existingHeight = coord[2]; // 检查是否已有高度信息

                                let height;
                                if (existingHeight !== undefined && existingHeight !== null) {
                                    // 如果坐标中已包含高度信息，使用该高度
                                    height = existingHeight;
                                } else {
                                    // 获取地形高程
                                    const cartographic = Cesium.Cartographic.fromDegrees(lon, lat);
                                    height = viewer.scene.globe.getHeight(cartographic) || 0;
                                    // 添加一个小的偏移量确保线条显示在地面上
                                    height += 2;
                                }

                                positions.push(Cesium.Cartesian3.fromDegrees(lon, lat, height));
                            });
                        });
                    }
                } else if (item.degrees && Array.isArray(item.degrees)) {
                    // 兼容原有的 degrees 数组格式
                    for (let i = 0; i < item.degrees.length; i += 2) {
                        const lon = item.degrees[i];
                        const lat = item.degrees[i + 1];

                        // 获取地形高程
                        const cartographic = Cesium.Cartographic.fromDegrees(lon, lat);
                        const height = viewer.scene.globe.getHeight(cartographic) || 0;

                        // 添加一个小的偏移量确保线条显示在地面上
                        const offsetHeight = height + 2;
                        positions.push(Cesium.Cartesian3.fromDegrees(lon, lat, offsetHeight));
                    }
                }

                findDataSource[0].entities.add({
                    id: item.id,
                    name: layerId,
                    show: true,
                    polyline: {
                        positions: positions,
                        material: Cesium.Color.fromCssColorString(item.colorStr).withAlpha(0.9),
                        distanceDisplayCondition: new Cesium.DistanceDisplayCondition(0.0, 1.5e4),
                        width: width,
                        // 启用贴地功能
                        clampToGround: false,
                        // 设置高度参考为地面
                        heightReference: Cesium.HeightReference.CLAMP_TO_GROUND,
                        // 启用深度测试以确保正确的遮挡关系
                        depthFailMaterial: Cesium.Color.fromCssColorString(item.colorStr).withAlpha(0.5)
                    },
                });
            });
        };

        // 从坐标获取位置
        this.getPositionFromCoord = (coord) => {
            const [lon, lat] = [coord[0], coord[1]];
            const existingHeight = coord[2];
            let height;

            if (existingHeight !== undefined && existingHeight !== null) {
                height = existingHeight;
            } else {
                const cartographic = Cesium.Cartographic.fromDegrees(lon, lat);
                height = (viewer.scene.globe.getHeight(cartographic) || 0) + 2;
            }

            return Cesium.Cartesian3.fromDegrees(lon, lat, height);
        };

        this.getCenterPoint = (coords) => {
            let totalLat = 0;
            let totalLng = 0;
            for (let i = 0; i < coords.length; i++) {
                totalLat += coords[i][1];
                totalLng += coords[i][0];
            }
            return [totalLng / coords.length, totalLat / coords.length];
        };

        // 动态计算偏移量
        this.calculateDynamicOffset = () => {
            // 根据地图模式获取正确的相机高度
            let cameraHeight;
            if (viewer.scene.mode === Cesium.SceneMode.SCENE2D) {
                cameraHeight = viewer.camera.getMagnitude();
            } else {
                cameraHeight = viewer.camera.positionCartographic.height;
            }

            const offsetMap = [
                { threshold: 500, value: -resize(60) },
                { threshold: 1000, value: -resize(75) },
                { threshold: 4000, value: -resize(65) },
                { threshold: 8000, value: -resize(55) },
                { threshold: 14000, value: -resize(45) },
            ];
            const entry = offsetMap.find((item) => cameraHeight < item.threshold);
            return entry ? entry.value : -resize(20);
        };

        // 动态计算字体大小
        this.calculateDynamicFontSize = () => {
            // 根据地图模式获取正确的相机高度
            let cameraHeight;
            if (viewer.scene.mode === Cesium.SceneMode.SCENE2D) {
                cameraHeight = viewer.camera.getMagnitude();
            } else {
                cameraHeight = viewer.camera.positionCartographic.height;
            }

            const sizeMap = [
                { threshold: 500, size: resize(16) },
                { threshold: 2000, size: resize(14) },
                { threshold: 10000, size: resize(12) },
            ];
            const entry = sizeMap.find((item) => cameraHeight < item.threshold);
            return entry ? entry.size : resize(10);
        };

        this.addPolygonGeometryFromDegrees = ({
                                                  layerId,
                                                  data,
                                                  show = true,
                                                  showLabel = false,
                                                  labelColor = "#ffffff",
                                              }) => {
            // 清除指定图层下的所有数据源实体;
            this.clearDataSourcesEntitiesByLayerId(layerId);

            const findDataSource = viewer.dataSources.getByName(layerId);
            if (findDataSource.length <= 0) {
                findDataSource[0] = new Cesium.CustomDataSource(layerId);
                viewer.dataSources.add(findDataSource[0]);
            }
            findDataSource.show = show;

            data.forEach((item) => {
                // 只支持Polygon和MultiPolygon
                if (
                    !item.geometry ||
                    !["Polygon", "MultiPolygon"].includes(item.geometry.type)
                )
                    return;
                let polygons = [];
                if (item.geometry.type === "Polygon") {
                    polygons = [item.geometry.coordinates];
                } else if (item.geometry.type === "MultiPolygon") {
                    polygons = item.geometry.coordinates;
                }
                polygons.forEach((polygonCoords, idx) => {
                    // 只取外环
                    const outerRing = polygonCoords[0];
                    const positions = outerRing.map((coord) =>
                        this.getPositionFromCoord(coord)
                    );
                    // 处理面颜色
                    const fillColor = item?.color || "#3880ea";
                    const outlineColor = item?.color || "#3880ea";
                    const outlineWidth = item?.outlineWidth || 2;

                    // 计算label属性
                    let label = undefined;
                    if (showLabel) {
                        const center = this.getCenterPoint(outerRing);
                        label = {
                            text: item.label || "",
                            font: new Cesium.CallbackProperty(
                                () =>
                                    `bold ${this.calculateDynamicFontSize()}px MicroSoft YaHei`,
                                false
                            ),
                            fillColor:
                                Cesium.Color.fromCssColorString(labelColor).withAlpha(1),
                            outlineColor: Cesium.Color.BLACK,
                            outlineWidth: 2,
                            style: Cesium.LabelStyle.FILL,
                            verticalOrigin: Cesium.VerticalOrigin.CENTER,
                            pixelOffset: new Cesium.CallbackProperty(
                                () => new Cesium.Cartesian2(0, this.calculateDynamicOffset()),
                                false
                            ),
                            disableDepthTestDistance: Number.POSITIVE_INFINITY,
                            showBackground: true,
                            backgroundColor: Cesium.Color.BLACK.withAlpha(0.5),
                            backgroundPadding: new Cesium.Cartesian2(10, 5),
                            showBorder: true,
                            borderColor:
                                Cesium.Color.fromCssColorString(labelColor).withAlpha(1),
                            borderWidth: 2,
                            distanceDisplayCondition: new Cesium.DistanceDisplayCondition(
                                0,
                                140000
                            ),
                            scaleByDistance: new Cesium.NearFarScalar(500, 1.2, 50000, 0.8),
                        };
                    }

                    // 创建面实体，label直接挂在entity上
                    findDataSource.entities.add({
                        id: item.id,
                        name: item.layerId,
                        show: true,
                        polygon: {
                            hierarchy: positions,
                            material:
                                Cesium.Color.fromCssColorString(fillColor).withAlpha(0.2),
                            outline: true,
                            outlineColor: Cesium.Color.fromCssColorString(outlineColor),
                            outlineWidth: outlineWidth,
                            heightReference: Cesium.HeightReference.CLAMP_TO_GROUND,
                            perPositionHeight: false,
                        },
                        position: showLabel
                            ? Cesium.Cartesian3.fromDegrees(...this.getCenterPoint(outerRing), 0)
                            : undefined,
                        label,
                        properties: { tag: item },
                    });
                });
            });
        };

        // todo 从指定图层获取实体
        this.getEntityFromTargetLayerById = (layerId, entityId) => {
            const findDataSource = viewer.dataSources.getByName(layerId);
            if (findDataSource.length <= 0) {
                return undefined;
            }
            const entity = findDataSource[0].entities.getById(entityId);
            return entity;
        }

        this.toggleLayerVisibleById = (layerId, show = true) => {
            const findDataSource = viewer.dataSources.getByName(layerId);
            if (findDataSource && findDataSource.length > 0) {
                findDataSource.forEach((dataSource) => {
                    dataSource.show = show;
                });
                // console.log("提示:toggleLayerVisibleById-图层未找到:", layerId);
            } else {
                // console.log("提示:toggleLayerVisibleById-图层未找到:", layerId);
            }
        }

        //定位图层范围
        this.focusLayerByLayerId = (
            layerId,
            {
                height = 1000,
                orientation = { heading: 0, pitch: -45, roll: 0 },
                duration = 1,
            }
        ) => {
            const findDataSource = viewer.dataSources.getByName(layerId);
            if (findDataSource && findDataSource.length > 0) {
                findDataSource.forEach((dataSource) => {
                    viewer.flyTo(dataSource, {
                        offset: {
                            heading: Cesium.Math.toRadians(orientation.heading),
                            pitch: Cesium.Math.toRadians(orientation.pitch),
                            roll: Cesium.Math.toRadians(orientation.roll),
                        },
                        maximumHeight: height,
                        duration: duration,
                    });
                });
            }
        };

        this.clearDataSourcesEntitiesByLayerId = (layerId) => {
            const findDataSource = viewer.dataSources.getByName(layerId);
            if (findDataSource && findDataSource.length > 0) {
                findDataSource.forEach((dataSource) => {
                    dataSource.show = false;
                    dataSource.entities.removeAll();
                });
            }
        };

        this.clearLayersByIds = (layers) => {
            for (let i = 0; i < layers.length; i++) {
                this.clearDataSourcesEntitiesByLayerId(layers[i]);
            }
        };

        this.clearAllDataSourcesEntities = () => {
            viewer.dataSources.removeAll();
        };

        // 强制渲染数据源中的所有实体
        this.forceRenderDataSource = (dataSource) => {
            if (dataSource && dataSource.entities) {
                // 强制更新所有实体的CallbackProperty
                dataSource.entities.values.forEach(entity => {
                    if (entity.billboard) {
                        // 触发billboard属性的更新
                        entity.billboard.show = entity.billboard.show;

                        // 在2D模式下，确保billboard没有缩放和距离限制
                        if (viewer.scene.mode === Cesium.SceneMode.SCENE2D) {
                            entity.billboard.scaleByDistance = undefined;
                            entity.billboard.distanceDisplayCondition = undefined;
                        }
                    }
                });

                // 强制渲染
                viewer.scene.requestRender();

                // 在2D模式下，减少渲染次数
                if (viewer.scene.mode === Cesium.SceneMode.SCENE2D) {
                    setTimeout(() => {
                        viewer.scene.requestRender();
                    }, 100);
                    setTimeout(() => {
                        viewer.scene.requestRender();
                    }, 300);
                }
            }
        };

        // 专门处理2D模式下的数据源渲染
        this.ensure2DRendering = (dataSource) => {
            if (viewer.scene.mode === Cesium.SceneMode.SCENE2D && dataSource && dataSource.entities) {
                dataSource.show = true;
                viewer.dataSources.raiseToTop(dataSource);

                // 批量处理所有实体
                dataSource.entities.values.forEach(entity => {
                    if (entity.billboard) {
                        // 只在需要时设置属性
                        if (entity.billboard.scaleByDistance !== undefined) {
                            entity.billboard.scaleByDistance = undefined;
                        }
                        if (entity.billboard.distanceDisplayCondition !== undefined) {
                            entity.billboard.distanceDisplayCondition = undefined;
                        }
                        if (entity.billboard.translucencyByDistance !== undefined) {
                            entity.billboard.translucencyByDistance = undefined;
                        }
                        if (entity.billboard.heightReference !== Cesium.HeightReference.NONE) {
                            entity.billboard.heightReference = Cesium.HeightReference.NONE;
                        }
                        if (entity.billboard.disableDepthTestDistance !== Number.POSITIVE_INFINITY) {
                            entity.billboard.disableDepthTestDistance = Number.POSITIVE_INFINITY;
                        }
                        entity.billboard.show = true;
                    }
                    entity.show = true;
                });

                // 减少渲染次数，只渲染2次
                viewer.scene.requestRender();
                setTimeout(() => {
                    viewer.scene.requestRender();
                    viewer.scene.forceResize && viewer.scene.forceResize();
                }, 150);
            }
        };

        this.highlightEntity = (entity) => {

            // 清除之前的高亮
            this.clearHighlight();

            // 隐藏原始实体
            entity.show = false;
            this.lastHighlightedEntity = entity;

            // 创建高亮实体
            const highlightEntity = new Cesium.Entity({
                id: entity.id + '_highlight',
                name: entity.name,
                show: true
            });

            // 复制位置
            if (entity.position) {
                highlightEntity.position = entity.position;
            }

            // 处理billboard类型的高亮
            if (entity.billboard) {
                // 创建简化的billboard配置，确保在2D模式下可见
                const billboardConfig = {
                    image: entity.billboard.image,
                    width: resize(entity.billboard.width) * 1.5,
                    height: resize(entity.billboard.height) * 1.5,
                    verticalOrigin: Cesium.VerticalOrigin.BOTTOM,
                    show: true,
                    disableDepthTestDistance: Number.POSITIVE_INFINITY,
                    heightReference: Cesium.HeightReference.RELATIVE_TO_GROUND,
                };

                // 根据地图模式设置不同的缩放参数
                if (viewer.scene.mode === Cesium.SceneMode.SCENE2D) {
                    // 2D模式：使用更保守的参数，确保近距离可见
                    billboardConfig.scaleByDistance = new Cesium.NearFarScalar(0, 1.0, 5000, 0.8);
                    billboardConfig.distanceDisplayCondition = new Cesium.DistanceDisplayCondition(0, 50000);
                } else {
                    // 3D模式：使用原有参数
                    billboardConfig.scaleByDistance = new Cesium.NearFarScalar(100, 1.5, 2e4, 0.3);
                    billboardConfig.distanceDisplayCondition = new Cesium.DistanceDisplayCondition(0, 1000000);
                }

                highlightEntity.billboard = billboardConfig;
            }

            // 处理polyline类型的高亮
            if (entity.polyline) {
                highlightEntity.polyline = {
                    positions: entity.polyline.positions,
                    material: Cesium.Color.AQUA.withAlpha(0.8),
                    width: entity.polyline.width * 1.5,
                    distanceDisplayCondition: entity.polyline.distanceDisplayCondition,
                    clampToGround: entity.polyline.clampToGround,
                    depthFailMaterial: Cesium.Color.AQUA.withAlpha(0.5)
                };
            }

            // 处理polygon类型的高亮
            if (entity.polygon) {
                highlightEntity.polygon = {
                    hierarchy: entity.polygon.hierarchy,
                    material: Cesium.Color.AQUA.withAlpha(0.3),
                    outline: true,
                    outlineColor: Cesium.Color.AQUA.withAlpha(0.8),
                    outlineWidth: entity.polygon.outlineWidth ? entity.polygon.outlineWidth * 1.5 : 2,
                    heightReference: entity.polygon.heightReference,
                    extrudedHeight: entity.polygon.extrudedHeight,
                    perPositionHeight: entity.polygon.perPositionHeight
                };
            }

            // 确保高亮数据源可见并添加到最顶层
            this.highlightDataSource.show = true;
            this.highlightDataSource.entities.removeAll();
            this.highlightDataSource.entities.add(highlightEntity);

            // 将高亮数据源移到最顶层
            viewer.dataSources.raiseToTop(this.highlightDataSource);

            // 强制刷新渲染
            viewer.scene.requestRender();

            // 在2D模式下，额外确保渲染
            if (viewer.scene.mode === Cesium.SceneMode.SCENE2D) {
                setTimeout(() => {
                    viewer.scene.requestRender();
                }, 50);
            }
        };

        this.clearHighlight = () => {

            // 恢复原始实体的显示和样式
            if (this.lastHighlightedEntity) {
                this.lastHighlightedEntity.show = true;

                // 如果使用了直接高亮方法，恢复原始样式
                if (this.lastHighlightedEntity._originalBillboard && this.lastHighlightedEntity.billboard) {
                    this.lastHighlightedEntity.billboard.width = this.lastHighlightedEntity._originalBillboard.width;
                    this.lastHighlightedEntity.billboard.height = this.lastHighlightedEntity._originalBillboard.height;
                    this.lastHighlightedEntity.billboard.scaleByDistance = this.lastHighlightedEntity._originalBillboard.scaleByDistance;
                    this.lastHighlightedEntity.billboard.distanceDisplayCondition = this.lastHighlightedEntity._originalBillboard.distanceDisplayCondition;
                    delete this.lastHighlightedEntity._originalBillboard;
                }

                this.lastHighlightedEntity = undefined;
            }

            // 清除高亮数据源中的所有实体
            this.highlightDataSource.entities.removeAll();

            // 强制刷新渲染
            viewer.scene.requestRender();
        };

        // 备用高亮方法：直接修改原始实体的样式
        this.highlightEntityDirect = (entity) => {
            console.log("使用直接高亮方法:", entity.id);

            // 清除之前的高亮
            this.clearHighlight();

            // 保存原始样式
            if (entity.billboard) {
                entity._originalBillboard = {
                    width: entity.billboard.width,
                    height: entity.billboard.height,
                    scaleByDistance: entity.billboard.scaleByDistance,
                    distanceDisplayCondition: entity.billboard.distanceDisplayCondition
                };

                // 直接修改原始实体的样式
                entity.billboard.width = resize(entity.billboard.width) * 1.5;
                entity.billboard.height = resize(entity.billboard.height) * 1.5;

                if (viewer.scene.mode === Cesium.SceneMode.SCENE2D) {
                    entity.billboard.scaleByDistance = new Cesium.NearFarScalar(0, 1.0, 5000, 0.8);
                    entity.billboard.distanceDisplayCondition = new Cesium.DistanceDisplayCondition(0, 50000);
                }

                this.lastHighlightedEntity = entity;
                viewer.scene.requestRender();
                console.log("直接高亮完成");
            }
        };

        this.getCluserCanvasImage = (num, hasAlarm, hasOffline) => {
            // 根据报警状态设置聚合颜色
            const clusterColor = hasAlarm ? "rgba(255, 0, 0, 1)" : hasOffline? "rgb(200, 200, 200, 1)" : "rgba(38, 138, 255, 1)";
            // 检查是否有报警状态的实体canvas图像缓存
            const numStr = hasAlarm ? num + "_1": hasOffline? num + "_0" : num + "";

            if (this.cluserCanvasImages[numStr]) {
                return this.cluserCanvasImages[numStr];
            }

            //创建cavas
            let canvas = document.createElement("canvas");
            //canvas大小
            let size = 10 * (num + "").length + 40;
            canvas.width = canvas.height = size;
            //获取canvas 上下文
            let ctx = canvas.getContext("2d");
            //绘制
            ctx.beginPath();
            ctx.globalAlpha = 0.5;
            (ctx.fillStyle = clusterColor),
                ctx.arc(size / 2, size / 2, size / 2 - 5, 0, 2 * Math.PI),
                ctx.fill(),
                ctx.beginPath(),
                (ctx.globalAlpha = 0.8),
                (ctx.fillStyle = clusterColor),
                ctx.arc(size / 2, size / 2, size / 2 - 10, 0, 2 * Math.PI),
                ctx.fill(),
                (ctx.font = "16px alpht"),
                (ctx.globalAlpha = 1),
                (ctx.fillStyle = "rgba(255,255,255,1)");
            let offset = size / 2 - (10 * num.toString().length) / 2;
            ctx.fillText(num, offset, size / 2 + 7);
            this.cluserCanvasImages[numStr] = canvas;
            return canvas;
        };

        this.getCustomCanvasImage = (num) => {
            // 根据报警状态设置聚合颜色
            let customColor = "rgb(47, 183, 183, 1)";
            if (num >= 90) {
                customColor = "rgba(38, 138, 255, 1)";
            } else if (num >= 80 && num < 90) {
                customColor = "rgba(255, 255, 0, 1)";
            } else if (num >= 70 && num < 80) {
                customColor = "rgba(245, 154, 35, 1)";
            } else if (num < 70) {
                customColor = "rgba(255, 0, 0, 1)";
            }

            // 检查是否有报警状态的实体canvas图像缓存
            const numStr = num + "_custom";

            if (this.customCanvasImages[numStr]) {
                return this.customCanvasImages[numStr];
            }
            //创建cavas
            let canvas = document.createElement("canvas");
            //canvas大小
            let size = 100;
            canvas.width = canvas.height = size;
            //获取canvas 上下文
            let ctx = canvas.getContext("2d");
            //绘制
            ctx.beginPath();
            ctx.globalAlpha = 0.5;
            (ctx.fillStyle = "rgba(4, 40, 77, 0.5)"),
                ctx.arc(size / 2, size / 2, size / 2 - 15, 0, 2 * Math.PI),
                ctx.fill(),
                ctx.beginPath(),
                (ctx.globalAlpha = 0.8),
                (ctx.fillStyle = customColor),
                ctx.arc(size / 2, size / 2, size / 2 - 20, 0, 2 * Math.PI),
                ctx.fill(),
                (ctx.font = "bold 20px alpht"),
                (ctx.globalAlpha = 1),
                (ctx.fillStyle = "rgba(4, 40, 77, 1)");
            let offset = size / 2 - (10 * num.toString().length) / 2;
            ctx.fillText(num, offset, size / 2 + 7);
            this.customCanvasImages[numStr] = canvas;
            return canvas;
        };

        this.addPoint = (degree) => {
            viewer.entities.add({
                position: Cesium.Cartesian3.fromDegrees(degree[0], degree[1]),
                billboard: {
                    image: imageInfo["point"],
                    width: 36,
                    height: 36,
                    verticalOrigin: Cesium.VerticalOrigin.BOTTOM,
                    // 根据地图模式调整缩放距离参数
                    scaleByDistance: new Cesium.NearFarScalar(100, 1.5, 2e4, 0.3),
                    disableDepthTestDistance: Number.POSITIVE_INFINITY,
                    heightReference: Cesium.HeightReference.RELATIVE_TO_GROUND,
                },
            });
        }

    }
}

class Primitive {
    constructor(viewer) {
        this.polylines = {}

        // 清除指定图层下的所有Primitive
        this.clearPrimitivesByLayerId = (layerId) => {
            // 清除billboard集合
            if (this[layerId + 'BillboardCollection']) {
                viewer.scene.primitives.remove(this[layerId + 'BillboardCollection']);
                this[layerId + 'BillboardCollection'] = null;
            }

            // 清除线集合
            if (this[layerId + 'PolylineCollection']) {
                viewer.scene.primitives.remove(this[layerId + 'PolylineCollection']);
                this[layerId + 'PolylineCollection'] = null;
            }

            // 清除面集合
            if (this[layerId + 'PolygonCollection']) {
                viewer.scene.primitives.remove(this[layerId + 'PolygonCollection']);
                this[layerId + 'PolygonCollection'] = null;
            }

            // 清除Primitive
            if (this[layerId + 'Primitive']) {
                viewer.scene.primitives.remove(this[layerId + 'Primitive']);
                this[layerId + 'Primitive'] = null;
            }

            // 清除事件处理器
            if (this[layerId + 'Handler']) {
                this[layerId + 'Handler'].destroy();
                this[layerId + 'Handler'] = null;
            }

            // 清除相机事件监听器
            if (this[layerId + '_cameraChangeHandler']) {
                viewer.camera.changed.removeEventListener(this[layerId + '_cameraChangeHandler']);
                this[layerId + '_cameraChangeHandler'] = null;
            }
        };

        this.clearAllPrimitives = () => {
            // 清理所有以指定后缀结尾的动态属性
            const suffixes = [
                'BillboardCollection',
                'PolylineCollection',
                'PolygonCollection',
                'Primitive',
                'Handler',
                '_cameraChangeHandler',
            ];
            Object.keys(this).forEach(key => {
                for (const suffix of suffixes) {
                    if (key.endsWith(suffix) && this[key]) {
                        // 处理不同类型的清理
                        if (suffix === 'Handler' && this[key].destroy) {
                            this[key].destroy();
                        } else if (
                            suffix === 'BillboardCollection' ||
                            suffix === 'PolylineCollection' ||
                            suffix === 'PolygonCollection' ||
                            suffix === 'Primitive'
                        ) {
                            if (viewer && viewer.scene && viewer.scene.primitives && this[key]) {
                                viewer.scene.primitives.remove(this[key]);
                            }
                        } else if (suffix === '_cameraChangeHandler') {
                            if (viewer && viewer.camera && this[key]) {
                                viewer.camera.changed.removeEventListener(this[key]);
                            }
                        }
                        this[key] = null;
                    }
                }
            });
            // 清理polylines对象中的GroundPolylinePrimitive
            if (this.polylines && typeof this.polylines === 'object') {
                Object.keys(this.polylines).forEach(type => {
                    const primitive = this.polylines[type];
                    if (primitive && viewer && viewer.scene && viewer.scene.primitives) {
                        viewer.scene.primitives.remove(primitive);
                    }
                });
                this.polylines = {};
            }
        }

        this.loadGroundPolylines = ({
                                        type,
                                        jsonData,
                                        show = true,
                                        outlineColor = Cesium.Color.fromCssColorString("#39A74A"),
                                        outlineWidth = 5,
                                        outlineType = "Solid",
                                        distanceDisplayCondition = new Cesium.DistanceDisplayConditionGeometryInstanceAttribute(
                                            0,
                                            Number.MAX_VALUE
                                        ),
                                    }) => {
            const instances = [];
            const features = jsonData.features;

            features.forEach((item) => {
                const type = item.geometry.type;
                const coordinates = item.geometry.coordinates;

                const polygons = [];
                if (type === "MultiPolygon") {
                    coordinates.forEach((item2) => {
                        polygons.push(item2);
                    });
                } else if (type === "Polygon") {
                    polygons.push(coordinates);
                } else if (type === "MultiLineString") {
                    polygons.push(coordinates);
                } else if (type === "LineString") {
                    polygons.push([coordinates]);
                }

                polygons.forEach((polygon, indexPolygon) => {
                    polygon.forEach((polyline, indexPolyline) => {
                        const geometryId =
                            item.properties.name + indexPolygon + indexPolyline;

                        const degrees = polyline.flat(2);
                        const positions = Cesium.Cartesian3.fromDegreesArray(degrees);

                        instances.push(
                            new Cesium.GeometryInstance({
                                geometry: new Cesium.GroundPolylineGeometry({
                                    positions,
                                    width: outlineWidth,
                                }),
                                attributes: {
                                    // @ts-ignore
                                    color: new Cesium.ColorGeometryInstanceAttribute.fromColor(
                                        outlineColor
                                    ),
                                    distanceDisplayCondition,
                                },
                                id: geometryId,
                            })
                        );
                    });
                });
            });

            let appearance = new Cesium.PolylineColorAppearance({
                translucent: false,
            });

            switch (outlineType) {
                case "Solid":
                    appearance = new Cesium.PolylineColorAppearance({
                        translucent: false,
                    });
                    break;
                case "Dash":
                    appearance = new Cesium.PolylineMaterialAppearance({
                        material: Cesium.Material.fromType(Cesium.Material.PolylineDashType, {
                            color: outlineColor,
                            gapColor: Cesium.Color.BLACK,
                            dashLength: 200,
                        }),
                    });
                    break;
                case "arrow":
                    appearance = new Cesium.PolylineMaterialAppearance({
                        material: new Cesium.Material({
                            fabric: {
                                type: "my-polyline-arrow",
                            },
                        }),
                    });
                    break;
            }

            this.polylines[type] = new Cesium.GroundPolylinePrimitive({
                geometryInstances: instances,
                appearance,
            });

            viewer.scene.primitives.add(this.polylines[type]);
            this.polylines[type].show = show;
        };

        this.loadPipeline = async ({
                                       type,
                                       url,
                                       outlineColor = Cesium.Color.fromCssColorString("#39A74A"),
                                       outlineWidth = 5,
                                   }) => {
            // @ts-ignore
            const jsonData = await Cesium.Resource.fetchJson(url);
            this.loadGroundPolylines({
                type,
                jsonData,
                outlineColor,
                outlineWidth,
                outlineType: "Solid",
            });
        };

        this.addPointPrimitiveFromDegrees = ({
                                                 layerId,
                                                 data,
                                                 width = 53,
                                                 height = 36,
                                                 show = true,
                                                 isCluster = true,
                                                 showLabel = false,
                                                 onClick = null, // 添加点击回调函数参数
                                             }) => {
            // 清除指定图层下的所有Primitive
            this.clearPrimitivesByLayerId(layerId);

            // 创建PrimitiveCollection用于管理整个图层
            if (!this[layerId + 'PrimitiveCollection']) {
                this[layerId + 'PrimitiveCollection'] = new Cesium.PrimitiveCollection();
                viewer.scene.primitives.add(this[layerId + 'PrimitiveCollection']);
            }

            // 清空现有集合
            this[layerId + 'PrimitiveCollection'].removeAll();

            // 创建BillboardCollection用于管理billboard
            const billboardCollection = new Cesium.BillboardCollection({
                scene: viewer.scene
            });

            // 创建LabelCollection用于管理标签
            const labelCollection = new Cesium.LabelCollection({
                scene: viewer.scene
            });

            // 创建事件处理器（如果不存在）
            if (!this[layerId + 'Handler']) {
                this[layerId + 'Handler'] = new Cesium.ScreenSpaceEventHandler(viewer.scene.canvas);
            } else {
                // 移除现有的点击事件处理
                this[layerId + 'Handler'].removeInputAction(Cesium.ScreenSpaceEventType.LEFT_CLICK);
            }

            // 存储点和原始数据的映射关系
            const pointsById = {};

            // 存储集合引用以便后续使用
            this[layerId + 'BillboardCollection'] = billboardCollection;
            this[layerId + 'LabelCollection'] = labelCollection;

            // 创建PrimitiveCluster用于聚合功能
            let primitiveCluster = null;
            if (isCluster) {
                primitiveCluster = new PrimitiveCluster();
                primitiveCluster.enabled = true;
                primitiveCluster.pixelRange = viewer.scene.mode === Cesium.SceneMode.SCENE2D ? 30 : 50;
                primitiveCluster.minimumClusterSize = 2;
                primitiveCluster._billboardCollection = billboardCollection;
                primitiveCluster._initialize(viewer.scene);

                // 添加聚合事件监听器
                primitiveCluster.clusterEvent.addEventListener((clusteredEntities, cluster) => {
                    // 关闭自带的显示聚合数量的标签
                    cluster.label.show = false;
                    cluster.billboard.show = true;
                    cluster.billboard.verticalOrigin = Cesium.VerticalOrigin.BOTTOM;
                    cluster.billboard.disableDepthTestDistance = Number.POSITIVE_INFINITY;

                    // 在二维模式下，确保聚合图标有合适的显示条件
                    if (viewer.scene.mode === Cesium.SceneMode.SCENE2D) {
                        // 2D模式：移除所有缩放和距离限制，只设置一次
                        if (cluster.billboard.scaleByDistance !== undefined) {
                            cluster.billboard.scaleByDistance = undefined;
                        }
                        if (cluster.billboard.distanceDisplayCondition !== undefined) {
                            cluster.billboard.distanceDisplayCondition = undefined;
                        }
                        if (cluster.billboard.translucencyByDistance !== undefined) {
                            cluster.billboard.translucencyByDistance = undefined;
                        }
                        if (cluster.billboard.disableDepthTestDistance !== Number.POSITIVE_INFINITY) {
                            cluster.billboard.disableDepthTestDistance = Number.POSITIVE_INFINITY;
                        }
                        if (cluster.billboard.heightReference !== Cesium.HeightReference.NONE) {
                            cluster.billboard.heightReference = Cesium.HeightReference.NONE;
                        }
                        cluster.billboard.show = true;
                    }

                    console.log('clusteredEntities---->>>1111', clusteredEntities);

                    // 检查是否有报警状态的实体
                    let hasAlarm = false;
                    let hasOffline = false;

                    for (let i = 0; i < clusteredEntities.length; i++) {
                        const entityId = clusteredEntities[i];
                        const entityData = pointsById[entityId];

                        if (!entityData) {
                            console.warn('Entity data not found for ID:', entityId);
                            continue;
                        }

                        const item = entityData.data;
                        console.log('Processing entity:', item.gisType, item);

                        if (devicesAlarmAll.includes(item.gisType)) {
                            // 设备类型实体
                            if (item.alarmStatus && item.alarmStatus !== "0") {
                                hasAlarm = true;
                                console.log('Found alarm entity:', item.gisType, item.alarmStatus);
                                break;
                            } else if (item.onlineStatus === "0") {
                                hasOffline = true;
                                console.log('Found offline entity:', item.gisType, item.onlineStatus);
                            }
                        } else if (videoMapAll.includes(item.gisType) || videoMapMisAll.includes(item.gisType)) {
                            // 视频监控类型实体
                            if (!item.online) {
                                hasOffline = true;
                                console.log('Found offline video entity:', item.gisType, item.online);
                                break;
                            }
                        }
                    }

                    console.log('Cluster status - hasAlarm:', hasAlarm, 'hasOffline:', hasOffline, 'count:', clusteredEntities.length);

                    // 生成聚合图标
                    cluster.billboard.image = this.createClusterCanvasImage(clusteredEntities.length, hasAlarm, hasOffline);
                });

                // 存储PrimitiveCluster引用
                this[layerId + 'PrimitiveCluster'] = primitiveCluster;
            }

            // 优化后的动态计算偏移量函数
            const calculateDynamicOffset = () => {
                // 根据地图模式获取正确的相机高度
                let cameraHeight;
                if (viewer.scene.mode === Cesium.SceneMode.SCENE2D) {
                    cameraHeight = viewer.camera.getMagnitude();
                } else {
                    cameraHeight = viewer.camera.positionCartographic.height;
                }

                // 计算图标高度（考虑缩放因素）
                const iconHeight = resize(height);
                let iconScale = 1.0;

                // 根据相机高度计算图标缩放比例
                if (viewer.scene.mode === Cesium.SceneMode.SCENE2D) {
                    // 2D模式下使用更保守的缩放
                    if (cameraHeight < 1000) {
                        iconScale = 1.0;
                    } else if (cameraHeight < 5000) {
                        iconScale = 0.8;
                    } else {
                        iconScale = 0.6;
                    }
                } else {
                    // 3D模式下的缩放计算
                    if (cameraHeight < 500) {
                        iconScale = 1.5;
                    } else if (cameraHeight < 1000) {
                        iconScale = 1.2;
                    } else if (cameraHeight < 4000) {
                        iconScale = 1.0;
                    } else if (cameraHeight < 8000) {
                        iconScale = 0.8;
                    } else if (cameraHeight < 14000) {
                        iconScale = 0.6;
                    } else {
                        iconScale = 0.4;
                    }
                }

                // 计算实际图标高度
                const actualIconHeight = iconHeight * iconScale;

                // 计算标签高度（考虑字体大小）
                const fontSize = calculateDynamicFontSize();
                const labelHeight = fontSize * 1.2; // 字体高度的估算

                // 计算标签背景高度（包括padding）
                const backgroundPadding = 10;
                const totalLabelHeight = labelHeight + (backgroundPadding * 2);

                // 优化偏移量计算：确保标签始终在图标正上方，间距稳定
                // fixedSpacing根据相机高度动态调整，避免重叠
                let fixedSpacing;
                if (cameraHeight < 1000) {
                    fixedSpacing = resize(20); // 近距离，间距大
                } else if (cameraHeight < 5000) {
                    fixedSpacing = resize(14); // 中距离，间距适中
                } else {
                    fixedSpacing = resize(8); // 远距离，间距小
                }
                // 计算偏移量：图标高度 + 固定间距
                // 由于Cesium坐标系统Y轴向下为正，所以返回负值
                const offsetY = actualIconHeight + fixedSpacing;

                return -offsetY;
            };

            // 动态计算billboard偏移量的函数
            const calculateDynamicPixelOffset = () => {
                // 根据地图模式获取正确的相机高度
                let cameraHeight;
                if (viewer.scene.mode === Cesium.SceneMode.SCENE2D) {
                    cameraHeight = viewer.camera.getMagnitude();
                } else {
                    cameraHeight = viewer.camera.positionCartographic.height;
                }

                let offsetY;
                if (cameraHeight < 500) {
                    offsetY = -resize(height * 6.0);
                } else if (cameraHeight < 1000) {
                    offsetY = -resize(height * 3.0);
                } else if (cameraHeight < 2000) {
                    offsetY = -resize(height * 2.0);
                }  else if (cameraHeight < 4000) {
                    offsetY = -resize(height * 1.5);
                }  else if (cameraHeight < 8000) {
                    offsetY = -resize(height * 1.0);
                } else if (cameraHeight < 14000) {
                    offsetY = -resize(height * 0.8);
                } else {
                    offsetY = -resize(height * 0.4);
                }

                return new Cesium.Cartesian2(0, offsetY);
            };

            // 优化后的动态计算字体大小函数
            const calculateDynamicFontSize = () => {
                // 根据地图模式获取正确的相机高度
                let cameraHeight;
                if (viewer.scene.mode === Cesium.SceneMode.SCENE2D) {
                    cameraHeight = viewer.camera.getMagnitude();
                } else {
                    cameraHeight = viewer.camera.positionCartographic.height;
                }

                let fontSize;
                if (cameraHeight < 500) {
                    fontSize = resize(16);
                } else if (cameraHeight < 2000) {
                    fontSize = resize(14);
                } else if (cameraHeight < 10000) {
                    fontSize = resize(12);
                } else {
                    fontSize = resize(10);
                }
                return fontSize;
            };

            // 处理点数据
            data.forEach((item) => {
                const position = Cesium.Cartesian3.fromDegrees(
                    parseFloat(item.longitude),
                    parseFloat(item.latitude)
                );

                // 创建billboard Primitive
                const billboard = billboardCollection.add({
                    position: position,
                    image: imageInfo[item.gisType],
                    width: resize(width),
                    height: resize(height),
                    verticalOrigin: Cesium.VerticalOrigin.BOTTOM,
                    // 在2D模式下，完全移除缩放和距离限制，确保始终可见
                    ...(viewer.scene.mode === Cesium.SceneMode.SCENE2D ? {
                        // 2D模式：不使用scaleByDistance和distanceDisplayCondition
                        disableDepthTestDistance: Number.POSITIVE_INFINITY,
                        heightReference: Cesium.HeightReference.RELATIVE_TO_GROUND,
                        show: true,
                    } : {
                        // 3D模式：保持原有参数
                        scaleByDistance: new Cesium.NearFarScalar(100, 1.5, 2e4, 0.3),
                        disableDepthTestDistance: Number.POSITIVE_INFINITY,
                        heightReference: Cesium.HeightReference.RELATIVE_TO_GROUND,
                        translucencyByDistance: new Cesium.NearFarScalar(
                            1.5e3,
                            1.0,
                            1.5e7,
                            0.2
                        ),
                        distanceDisplayCondition: new Cesium.DistanceDisplayCondition(0, 1000000),
                        show: true,
                    }),
                    id: item.id,
                    name: layerId,
                    show: show
                });

                // 如果需要显示标签，创建标签Primitive
                if (showLabel && item.name) {
                    const label = labelCollection.add({
                        position: position,
                        text: item.name,
                        font: new Cesium.CallbackProperty(() => {
                            return `bold ${calculateDynamicFontSize()}px MicroSoft YaHei`;
                        }, false),
                        verticalOrigin: Cesium.VerticalOrigin.BOTTOM, // 标签底部对齐
                        horizontalOrigin: Cesium.HorizontalOrigin.CENTER, // 确保水平居中
                        fillColor: Cesium.Color.fromCssColorString('#3880ea').withAlpha(1),
                        outlineColor: Cesium.Color.BLACK,
                        outlineWidth: 2,
                        style: Cesium.LabelStyle.FILL,
                        pixelOffset: new Cesium.CallbackProperty(() => {
                            return calculateDynamicPixelOffset();
                        }, false),
                        disableDepthTestDistance: Number.POSITIVE_INFINITY,
                        showBackground: true,
                        backgroundColor: Cesium.Color.BLACK.withAlpha(0.5),
                        backgroundPadding: new Cesium.Cartesian2(10, 5),
                        showBorder: true,
                        borderColor: Cesium.Color.fromCssColorString('#3880ea').withAlpha(1),
                        borderWidth: 2,
                        distanceDisplayCondition: new Cesium.DistanceDisplayCondition(0, 14000),
                        id: item.id + '_label',
                        show: show
                    });
                }

                // 存储billboard和原始数据的映射关系
                pointsById[item.id] = {
                    billboard: billboard,
                    data: item,
                    properties: {
                        tag: item,
                        layerId: layerId
                    }
                };
            });

            // 将集合添加到PrimitiveCollection中
            this[layerId + 'PrimitiveCollection'].add(billboardCollection);
            this[layerId + 'PrimitiveCollection'].add(labelCollection);

            // 如果启用了聚合，添加PrimitiveCluster
            if (isCluster && primitiveCluster) {
                this[layerId + 'PrimitiveCollection'].add(primitiveCluster);
            }

            // 动态控制聚合效果
            const updateClustering = () => {
                // 根据地图模式获取正确的相机高度
                let cameraHeight;
                if (viewer.scene.mode === Cesium.SceneMode.SCENE2D) {
                    // 二维模式下使用相机的magnitude作为高度
                    cameraHeight = viewer.camera.getMagnitude();
                } else {
                    // 三维模式下使用positionCartographic.height
                    cameraHeight = viewer.camera.positionCartographic.height;
                }

                // 在二维模式下使用5000作为聚合阈值，在三维模式下使用500作为聚合阈值
                const shouldCluster = isCluster && cameraHeight >= (viewer.scene.mode === Cesium.SceneMode.SCENE2D ? 5000 : 500);

                // 使用PrimitiveCluster的方式管理聚合
                if (primitiveCluster) {
                    primitiveCluster.enabled = shouldCluster;

                    // 根据相机高度调整聚合参数
                    if (shouldCluster) {
                        primitiveCluster.pixelRange = viewer.scene.mode === Cesium.SceneMode.SCENE2D ? 30 : 50;
                        primitiveCluster.minimumClusterSize = 2;
                    }
                }
            };



            // 初始设置聚合状态
            updateClustering();
            // 点位加载完成强制刷新渲染
            viewer.scene.requestRender();

            // 如果提供了点击回调函数，添加点击事件处理
            if (onClick && typeof onClick === 'function') {
                this[layerId + 'Handler'].setInputAction((movement) => {
                    const pickedObject = viewer.scene.pick(movement.position);

                    if (pickedObject && pickedObject.id) {
                        const entityId = pickedObject.id;

                        // 检查是否是聚合billboard
                        if (entityId.includes('_cluster')) {
                            // 处理聚合点击事件
                            const clusterId = entityId.replace('cluster_', '');
                            // 这里可以添加聚合点击的特殊处理逻辑
                            // 例如显示聚合详情或执行其他操作
                            onClick({
                                type: 'cluster',
                                position: pickedObject.position,
                                clusterId: clusterId,
                                billboard: pickedObject
                            });
                        } else {
                            // 处理单个billboard点击事件
                            // 移除标签后缀以获取原始ID
                            const originalId = entityId.replace('_label', '');
                            const entityData = pointsById[originalId] ? pointsById[originalId].data : null;

                            if (entityData) {
                                // 获取点击位置的经纬度
                                const cartesian = viewer.scene.pickPosition(movement.position);
                                let position = cartesian;

                                // 如果pickPosition失败，尝试使用globe.pick
                                if (!cartesian) {
                                    position = viewer.scene.globe.pick(
                                        viewer.camera.getPickRay(movement.position),
                                        viewer.scene
                                    );
                                }

                                // 调用回调函数，传递实体数据和点击位置
                                onClick({
                                    type: 'billboard',
                                    position: position,
                                    entity: entityData,
                                    properties: pointsById[originalId].properties
                                });
                            }
                        }
                    }
                }, Cesium.ScreenSpaceEventType.LEFT_CLICK);
            }

            // 监听相机移动事件，动态更新聚合状态
            const cameraChangeHandler = () => {
                const oldClusterEnabled = isCluster && viewer.camera.positionCartographic.height >= (viewer.scene.mode === Cesium.SceneMode.SCENE2D ? 5000 : 500);
                updateClustering();

                // 只在聚合状态发生变化时才强制渲染
                const newClusterEnabled = isCluster && viewer.camera.positionCartographic.height >= (viewer.scene.mode === Cesium.SceneMode.SCENE2D ? 5000 : 500);
                if (oldClusterEnabled !== newClusterEnabled) {
                    viewer.scene.requestRender();
                }
            };

            // 移除之前的相机事件监听器（如果存在）
            if (this[layerId + '_cameraChangeHandler']) {
                viewer.camera.changed.removeEventListener(this[layerId + '_cameraChangeHandler']);
            }

            // 添加相机变化监听器
            viewer.camera.changed.addEventListener(cameraChangeHandler);
            // 保存监听器引用以便后续移除
            this[layerId + '_cameraChangeHandler'] = cameraChangeHandler;

            return {
                primitiveCollection: this[layerId + 'PrimitiveCollection'],
                billboardCollection: billboardCollection,
                labelCollection: labelCollection,
                primitiveCluster: primitiveCluster,
                handler: this[layerId + 'Handler'], // 返回事件处理器以便外部管理
                pointsById: pointsById // 返回点数据映射关系
            };
        };

        // 为Primitive方式创建聚合canvas图像
        this.createClusterCanvasImage = (num, hasAlarm, hasOffline) => {
            // 根据报警状态设置聚合颜色
            let clusterColor;
            if (hasAlarm) {
                clusterColor = "rgba(255, 0, 0, 1)"; // 红色 - 报警状态
            } else if (hasOffline) {
                clusterColor = "rgba(128, 128, 128, 1)"; // 灰色 - 离线状态
            } else {
                clusterColor = "rgba(38, 138, 255, 1)"; // 蓝色 - 正常状态
            }

            //创建canvas
            let canvas = document.createElement("canvas");
            //canvas大小
            let size = 10 * (num + "").length + 40;
            canvas.width = canvas.height = size;
            //获取canvas 上下文
            let ctx = canvas.getContext("2d");

            //绘制外圆
            ctx.beginPath();
            ctx.globalAlpha = 0.5;
            ctx.fillStyle = clusterColor;
            ctx.arc(size / 2, size / 2, size / 2 - 5, 0, 2 * Math.PI);
            ctx.fill();

            //绘制内圆
            ctx.beginPath();
            ctx.globalAlpha = 0.8;
            ctx.fillStyle = clusterColor;
            ctx.arc(size / 2, size / 2, size / 2 - 10, 0, 2 * Math.PI);
            ctx.fill();

            //绘制文字
            ctx.font = "16px Microsoft YaHei";
            ctx.globalAlpha = 1;
            ctx.fillStyle = "rgba(255,255,255,1)";
            ctx.textAlign = "center";
            ctx.textBaseline = "middle";
            ctx.fillText(num, size / 2, size / 2);

            return canvas;
        };

        this.addPolylinePrimitiveFromDegrees = ({
                                                    layerId,
                                                    data,
                                                    material,
                                                    width = 4,
                                                    show = true,
                                                    outlineColor = Cesium.Color.fromCssColorString("#39A74A"),
                                                    outlineType = "Solid",
                                                    onClick = null, // 添加点击回调函数参数
                                                }) => {
            // 清除指定图层下的所有Primitive
            this.clearPrimitivesByLayerId(layerId);

            // 创建或获取线集合
            if (!this[layerId + 'PolylineCollection']) {
                this[layerId + 'PolylineCollection'] = new Cesium.PolylineCollection({
                    scene: viewer.scene
                });
                viewer.scene.primitives.add(this[layerId + 'PolylineCollection']);
            }

            // 清空现有集合
            this[layerId + 'PolylineCollection'].removeAll();

            // 创建事件处理器（如果不存在）
            if (!this[layerId + 'Handler']) {
                this[layerId + 'Handler'] = new Cesium.ScreenSpaceEventHandler(viewer.scene.canvas);
            } else {
                // 移除现有的点击事件处理
                this[layerId + 'Handler'].removeInputAction(Cesium.ScreenSpaceEventType.LEFT_CLICK);
            }

            // 存储线和原始数据的映射关系
            const polylinesById = {};

            // 处理线数据
            data.forEach(item => {
                // 将经纬度坐标转换为带高程的坐标数组
                const positions = [];

                // 根据 item.geometry 类型处理坐标数据
                if (item.geometry && item.geometry.coordinates) {
                    const coordinates = item.geometry.coordinates;

                    // 处理不同的几何类型
                    if (item.geometry.type === 'LineString') {
                        // LineString: coordinates 是一个坐标数组 [[lon, lat], [lon, lat], ...]
                        coordinates.forEach(coord => {
                            const lon = coord[0];
                            const lat = coord[1];
                            const existingHeight = coord[2]; // 检查是否已有高度信息

                            let height;
                            if (existingHeight !== undefined && existingHeight !== null) {
                                // 如果坐标中已包含高度信息，使用该高度
                                height = existingHeight;
                            } else {
                                // 获取地形高程
                                const cartographic = Cesium.Cartographic.fromDegrees(lon, lat);
                                height = viewer.scene.globe.getHeight(cartographic) || 0;
                                // 添加一个小的偏移量确保线条显示在地面上
                                height += 2;
                            }

                            positions.push(Cesium.Cartesian3.fromDegrees(lon, lat, height));
                        });
                    } else if (item.geometry.type === 'MultiLineString') {
                        // MultiLineString: coordinates 是一个线串数组 [[[lon, lat], [lon, lat]], [[lon, lat], [lon, lat]]]
                        coordinates.forEach(lineString => {
                            lineString.forEach(coord => {
                                const lon = coord[0];
                                const lat = coord[1];
                                const existingHeight = coord[2]; // 检查是否已有高度信息

                                let height;
                                if (existingHeight !== undefined && existingHeight !== null) {
                                    // 如果坐标中已包含高度信息，使用该高度
                                    height = existingHeight;
                                } else {
                                    // 获取地形高程
                                    const cartographic = Cesium.Cartographic.fromDegrees(lon, lat);
                                    height = viewer.scene.globe.getHeight(cartographic) || 0;
                                    // 添加一个小的偏移量确保线条显示在地面上
                                    height += 2;
                                }

                                positions.push(Cesium.Cartesian3.fromDegrees(lon, lat, height));
                            });
                        });
                    }
                } else if (item.degrees && Array.isArray(item.degrees)) {
                    // 兼容原有的 degrees 数组格式
                    for (let i = 0; i < item.degrees.length; i += 2) {
                        const lon = item.degrees[i];
                        const lat = item.degrees[i + 1];

                        // 获取地形高程
                        const cartographic = Cesium.Cartographic.fromDegrees(lon, lat);
                        const height = viewer.scene.globe.getHeight(cartographic) || 0;

                        // 添加一个小的偏移量确保线条显示在地面上
                        const offsetHeight = height + 2;
                        positions.push(Cesium.Cartesian3.fromDegrees(lon, lat, offsetHeight));
                    }
                }

                // 创建线Primitive
                const polyline = this[layerId + 'PolylineCollection'].add({
                    positions: positions,
                    width: width + 1,
                    material: Cesium.Color.fromCssColorString(item.colorStr || "#39A74A").withAlpha(0.9),
                    distanceDisplayCondition: new Cesium.DistanceDisplayCondition(0.0, 1.5e4),
                    clampToGround: false,
                    heightReference: Cesium.HeightReference.CLAMP_TO_GROUND,
                    id: item.id,
                    show: show
                });

                // 存储线和原始数据的映射关系
                polylinesById[item.id] = {
                    polyline: polyline,
                    positions: positions,
                    data: item,
                    properties: {
                        tag: item,
                        layerId: layerId
                    }
                };
            });

            // 如果提供了点击回调函数，添加点击事件处理
            if (onClick && typeof onClick === 'function') {
                this[layerId + 'Handler'].setInputAction((movement) => {
                    const pickedObject = viewer.scene.pick(movement.position);

                    if (pickedObject && pickedObject.id) {
                        const entityId = pickedObject.id;
                        const entityData = polylinesById[entityId] ? polylinesById[entityId].data : null;

                        if (entityData) {
                            // 获取点击位置的经纬度
                            const cartesian = viewer.scene.pickPosition(movement.position);
                            let position = cartesian;

                            // 如果pickPosition失败，尝试使用globe.pick
                            if (!cartesian) {
                                position = viewer.scene.globe.pick(
                                    viewer.camera.getPickRay(movement.position),
                                    viewer.scene
                                );
                            }

                            // 调用回调函数，传递实体数据和点击位置
                            onClick({
                                type: 'polyline',
                                position: position,
                                entity: entityData,
                                properties: polylinesById[entityId].properties
                            });
                        }
                    }
                }, Cesium.ScreenSpaceEventType.LEFT_CLICK);
            }

            // 监听相机移动事件，动态更新显示
            const cameraChangeHandler = () => {
                // 根据相机高度动态调整线的显示
                let cameraHeight;
                if (viewer.scene.mode === Cesium.SceneMode.SCENE2D) {
                    cameraHeight = viewer.camera.getMagnitude();
                } else {
                    cameraHeight = viewer.camera.positionCartographic.height;
                }

                // 动态调整线的宽度和透明度
                const polylines = this[layerId + 'PolylineCollection'].get();
                polylines.forEach(polyline => {
                    if (polyline.id && polylinesById[polyline.id]) {
                        const item = polylinesById[polyline.id].data;

                        // 根据相机高度调整宽度
                        let dynamicWidth = width + 1;
                        if (cameraHeight < 1000) {
                            dynamicWidth = width + 2;
                        } else if (cameraHeight < 5000) {
                            dynamicWidth = width + 1;
                        } else {
                            dynamicWidth = width;
                        }

                        polyline.width = dynamicWidth;

                        // 根据相机高度调整透明度
                        let alpha = 0.9;
                        if (cameraHeight > 10000) {
                            alpha = 0.7;
                        } else if (cameraHeight > 5000) {
                            alpha = 0.8;
                        }

                        polyline.material = Cesium.Color.fromCssColorString(item.colorStr || "#39A74A").withAlpha(alpha);
                    }
                });
            };

            // 移除之前的相机事件监听器（如果存在）
            if (this[layerId + '_cameraChangeHandler']) {
                viewer.camera.changed.removeEventListener(this[layerId + '_cameraChangeHandler']);
            }

            // 添加相机变化监听器
            viewer.camera.changed.addEventListener(cameraChangeHandler);
            // 保存监听器引用以便后续移除
            this[layerId + '_cameraChangeHandler'] = cameraChangeHandler;

            // 初始调用一次以设置初始状态
            cameraChangeHandler();

            return {
                collection: this[layerId + 'PolylineCollection'],
                handler: this[layerId + 'Handler'], // 返回事件处理器以便外部管理
                polylinesById: polylinesById // 返回线数据映射关系
            };
        };

        this.addPolygonPrimitiveFromDegrees = ({
                                                   layerId,
                                                   data,
                                                   show = true,
                                                   showLabel = false,
                                                   labelColor = "#ffffff",
                                                   onClick = null, // 添加点击回调函数参数
                                               }) => {
            // 清除指定图层下的所有Primitive
            this.clearPrimitivesByLayerId(layerId);

            // 创建或获取面集合
            if (!this[layerId + 'PolygonCollection']) {
                this[layerId + 'PolygonCollection'] = new Cesium.PolygonCollection({
                    scene: viewer.scene
                });
                viewer.scene.primitives.add(this[layerId + 'PolygonCollection']);
            }

            // 清空现有集合
            this[layerId + 'PolygonCollection'].removeAll();

            // 创建事件处理器（如果不存在）
            if (!this[layerId + 'Handler']) {
                this[layerId + 'Handler'] = new Cesium.ScreenSpaceEventHandler(viewer.scene.canvas);
            } else {
                // 移除现有的点击事件处理
                this[layerId + 'Handler'].removeInputAction(Cesium.ScreenSpaceEventType.LEFT_CLICK);
            }

            // 存储面和原始数据的映射关系
            const polygonsById = {};

            // 处理面数据
            data.forEach((item) => {
                // 只支持Polygon和MultiPolygon
                if (
                    !item.geometry ||
                    !["Polygon", "MultiPolygon"].includes(item.geometry.type)
                )
                    return;

                let polygons = [];
                if (item.geometry.type === "Polygon") {
                    polygons = [item.geometry.coordinates];
                } else if (item.geometry.type === "MultiPolygon") {
                    polygons = item.geometry.coordinates;
                }

                polygons.forEach((polygonCoords, idx) => {
                    // 只取外环
                    const outerRing = polygonCoords[0];
                    const positions = outerRing.map((coord) => {
                        const lon = coord[0];
                        const lat = coord[1];
                        const existingHeight = coord[2];

                        let height;
                        if (existingHeight !== undefined && existingHeight !== null) {
                            height = existingHeight;
                        } else {
                            const cartographic = Cesium.Cartographic.fromDegrees(lon, lat);
                            height = viewer.scene.globe.getHeight(cartographic) || 0;
                            height += 2;
                        }

                        return Cesium.Cartesian3.fromDegrees(lon, lat, height);
                    });

                    // 处理面颜色
                    const fillColor = item?.color || "#3880ea";
                    const outlineColor = item?.color || "#3880ea";
                    const outlineWidth = item?.outlineWidth || 2;

                    // 创建面Primitive
                    const polygon = this[layerId + 'PolygonCollection'].add({
                        hierarchy: positions,
                        material: Cesium.Color.fromCssColorString(fillColor).withAlpha(0.2),
                        outline: true,
                        outlineColor: Cesium.Color.fromCssColorString(outlineColor),
                        outlineWidth: outlineWidth,
                        heightReference: Cesium.HeightReference.CLAMP_TO_GROUND,
                        perPositionHeight: false,
                        id: item.id,
                        show: show
                    });

                    // 存储面和原始数据的映射关系
                    polygonsById[item.id] = {
                        polygon: polygon,
                        positions: positions,
                        data: item,
                        properties: {
                            tag: item,
                            layerId: layerId
                        }
                    };

                    // 如果需要显示标签，创建标签Primitive
                    if (showLabel && item.label) {
                        const center = this.getCenterPoint(outerRing);
                        const label = this[layerId + 'PolygonCollection'].add({
                            position: Cesium.Cartesian3.fromDegrees(center[0], center[1], 0),
                            text: item.label,
                            font: new Cesium.CallbackProperty(() => {
                                return `bold ${this.calculateDynamicFontSize()}px MicroSoft YaHei`;
                            }, false),
                            fillColor: Cesium.Color.fromCssColorString(labelColor).withAlpha(1),
                            outlineColor: Cesium.Color.BLACK,
                            outlineWidth: 2,
                            style: Cesium.LabelStyle.FILL,
                            verticalOrigin: Cesium.VerticalOrigin.CENTER,
                            pixelOffset: new Cesium.CallbackProperty(() => {
                                return new Cesium.Cartesian2(0, this.calculateDynamicOffset());
                            }, false),
                            disableDepthTestDistance: Number.POSITIVE_INFINITY,
                            showBackground: true,
                            backgroundColor: Cesium.Color.BLACK.withAlpha(0.5),
                            backgroundPadding: new Cesium.Cartesian2(10, 5),
                            showBorder: true,
                            borderColor: Cesium.Color.fromCssColorString(labelColor).withAlpha(1),
                            borderWidth: 2,
                            distanceDisplayCondition: new Cesium.DistanceDisplayCondition(0, 140000),
                            scaleByDistance: new Cesium.NearFarScalar(500, 1.2, 50000, 0.8),
                            id: item.id + '_label',
                            show: show
                        });
                    }
                });
            });

            // 如果提供了点击回调函数，添加点击事件处理
            if (onClick && typeof onClick === 'function') {
                this[layerId + 'Handler'].setInputAction((movement) => {
                    const pickedObject = viewer.scene.pick(movement.position);

                    if (pickedObject && pickedObject.id) {
                        const entityId = pickedObject.id;
                        // 移除标签后缀以获取原始ID
                        const originalId = entityId.replace('_label', '');
                        const entityData = polygonsById[originalId] ? polygonsById[originalId].data : null;

                        if (entityData) {
                            // 获取点击位置的经纬度
                            const cartesian = viewer.scene.pickPosition(movement.position);
                            let position = cartesian;

                            // 如果pickPosition失败，尝试使用globe.pick
                            if (!cartesian) {
                                position = viewer.scene.globe.pick(
                                    viewer.camera.getPickRay(movement.position),
                                    viewer.scene
                                );
                            }

                            // 调用回调函数，传递实体数据和点击位置
                            onClick({
                                type: 'polygon',
                                position: position,
                                entity: entityData,
                                properties: polygonsById[originalId].properties
                            });
                        }
                    }
                }, Cesium.ScreenSpaceEventType.LEFT_CLICK);
            }

            // 监听相机移动事件，动态更新显示
            const cameraChangeHandler = () => {
                // 根据相机高度动态调整面的显示
                let cameraHeight;
                if (viewer.scene.mode === Cesium.SceneMode.SCENE2D) {
                    cameraHeight = viewer.camera.getMagnitude();
                } else {
                    cameraHeight = viewer.camera.positionCartographic.height;
                }

                // 动态调整面的透明度和轮廓宽度
                const polygons = this[layerId + 'PolygonCollection'].get();
                polygons.forEach(polygon => {
                    if (polygon.id && !polygon.id.includes('_label') && polygonsById[polygon.id]) {
                        const item = polygonsById[polygon.id].data;

                        // 根据相机高度调整透明度
                        let alpha = 0.2;
                        if (cameraHeight > 10000) {
                            alpha = 0.1;
                        } else if (cameraHeight > 5000) {
                            alpha = 0.15;
                        }

                        polygon.material = Cesium.Color.fromCssColorString(item.color || "#3880ea").withAlpha(alpha);

                        // 根据相机高度调整轮廓宽度
                        let outlineWidth = item.outlineWidth || 2;
                        if (cameraHeight > 10000) {
                            outlineWidth = 1;
                        } else if (cameraHeight > 5000) {
                            outlineWidth = 1.5;
                        }

                        polygon.outlineWidth = outlineWidth;
                    }
                });
            };

            // 移除之前的相机事件监听器（如果存在）
            if (this[layerId + '_cameraChangeHandler']) {
                viewer.camera.changed.removeEventListener(this[layerId + '_cameraChangeHandler']);
            }

            // 添加相机变化监听器
            viewer.camera.changed.addEventListener(cameraChangeHandler);
            // 保存监听器引用以便后续移除
            this[layerId + '_cameraChangeHandler'] = cameraChangeHandler;

            // 初始调用一次以设置初始状态
            cameraChangeHandler();

            return {
                collection: this[layerId + 'PolygonCollection'],
                handler: this[layerId + 'Handler'], // 返回事件处理器以便外部管理
                polygonsById: polygonsById // 返回面数据映射关系
            };
        };


        this.getPrimitiveFromTargetLayerById = (layerId, id) => {
            // 查找billboard集合
            const billboardCollection = this[layerId + 'BillboardCollection'];
            if (billboardCollection) {
                const billboards = billboardCollection.get();
                for (let i = 0; i < billboards.length; i++) {
                    if (billboards[i].id === id) {
                        return {
                            type: 'billboard',
                            primitive: billboards[i],
                            collection: billboardCollection
                        };
                    }
                }
            }

            // 查找polyline集合
            const polylineCollection = this[layerId + 'PolylineCollection'];
            if (polylineCollection) {
                const polylines = polylineCollection.get();
                for (let i = 0; i < polylines.length; i++) {
                    if (polylines[i].id === id) {
                        return {
                            type: 'polyline',
                            primitive: polylines[i],
                            collection: polylineCollection
                        };
                    }
                }
            }

            // 查找polygon集合
            const polygonCollection = this[layerId + 'PolygonCollection'];
            if (polygonCollection) {
                const polygons = polygonCollection.get();
                for (let i = 0; i < polygons.length; i++) {
                    if (polygons[i].id === id) {
                        return {
                            type: 'polygon',
                            primitive: polygons[i],
                            collection: polygonCollection
                        };
                    }
                }
            }

            // 查找Primitive对象
            const primitive = this[layerId + 'Primitive'];
            if (primitive) {
                // 对于GroundPolylinePrimitive，需要检查几何实例
                if (primitive.geometryInstances) {
                    const instances = primitive.geometryInstances;
                    for (let i = 0; i < instances.length; i++) {
                        if (instances[i].id === id) {
                            return {
                                type: 'groundPolyline',
                                primitive: primitive,
                                geometryInstance: instances[i]
                            };
                        }
                    }
                }
            }

            // 如果都没找到，返回undefined
            return undefined;
        };

        this.togglePrimitiveLayerVisibleById = (layerId, show = true) => {
            // 控制点图层显示/隐藏
            if (this[layerId + 'BillboardCollection']) {
                this[layerId + 'BillboardCollection'].show = show;
            }
            // 控制线图层显示/隐藏
            if (this[layerId + 'PolylineCollection']) {
                this[layerId + 'PolylineCollection'].show = show;
            }
            // 控制面图层显示/隐藏
            if (this[layerId + 'PolygonCollection']) {
                this[layerId + 'PolygonCollection'].show = show;
            }
            // 控制Primitive对象（如GroundPolylinePrimitive等）显示/隐藏
            if (this[layerId + 'Primitive']) {
                this[layerId + 'Primitive'].show = show;
            }
        }

    }

}

class Event {
    constructor(viewer) {
        this.pickPosition = [];
        this.handler = new Cesium.ScreenSpaceEventHandler(viewer.scene.canvas);
        this.callbackArrayOfPick = []
        //callbackArray：点击事件，hoverArray: 悬停事件

        this.activatePickHandler = (
            callbackArray,
            callbackHoverArray
        ) => {
            let callbackArrayOfPick = [];
            const pick = (movement) => {
                const cartesian3 = viewer.scene.globe.pick(
                    viewer.camera.getPickRay(movement.position),
                    viewer.scene
                );
                // @ts-ignore
                const degree = cartesian3ToDegree(cartesian3);
                this.pickPosition = degree;
                const pickedObject = viewer.scene.pick(movement.position);
                callbackArray[3](degree);
                if (
                    pickedObject &&
                    pickedObject.id instanceof Cesium.Entity &&
                    pickedObject.id.billboard
                ) {
                    callbackArray[0](pickedObject.id);
                } else if (
                    pickedObject &&
                    pickedObject.id instanceof Cesium.Entity &&
                    pickedObject.id.polyline
                ) {
                    callbackArray[0](pickedObject.id);
                } else if (
                    pickedObject &&
                    pickedObject.id instanceof Cesium.Entity &&
                    pickedObject.id.label
                ) {
                    callbackArray[2](pickedObject.id);
                }
                /*  else if (
                      pickedObject &&
                      pickedObject instanceof Cesium.Cesium3DTileFeature
                  ) {
                    callbackArray[5](pickedObject, degree);
                  } */
                else if (typeof pickedObject === "undefined") {
                    callbackArray[4]();
                }
            };
            callbackArrayOfPick = callbackArray;
            this.handler.setInputAction(pick, Cesium.ScreenSpaceEventType.LEFT_CLICK);

            const move = (movement) => {
                const hoverPosition = {x: 400, y: 200};
                const pickedObject = viewer.scene.pick(movement.endPosition);

                if (
                    pickedObject &&
                    pickedObject.id instanceof Cesium.Entity &&
                    pickedObject.id.polyline
                ) {
                    const cartesian3 = viewer.scene.globe.pick(
                        viewer.camera.getPickRay(movement.endPosition),
                        viewer.scene
                    );
                    Cesium.SceneTransforms.wgs84ToWindowCoordinates(
                        viewer.scene,
                        cartesian3,
                        hoverPosition
                    );
                    const pipename = pickedObject.id.properties.name._value;
                    callbackHoverArray[0](
                        pipename,
                        true,
                        hoverPosition.x + 20 + "px",
                        hoverPosition.y + 20 + "px"
                    );
                } else {
                    callbackHoverArray[0]("pipename", false, "800px", "600px");
                }
            };
            // handler.setInputAction(move, Cesium.ScreenSpaceEventType.MOUSE_MOVE);
        };

        this.inactivatePickHandler = () => {
            this.handler.removeInputAction(Cesium.ScreenSpaceEventType.LEFT_CLICK);
        };

        this.keyboard = () => {
            const canvas = viewer.canvas;
            canvas.setAttribute("tabindex", "0");
            canvas.onclick = function () {
                canvas.focus();
            };
            const ellipsoid = viewer.scene.globe.ellipsoid;
            const flags = {
                moveForward: false,
                moveBackward: false,
                moveUp: false,
                moveDown: false,
                moveLeft: false,
                moveRight: false,
                lookUp: false,
                lookDown: false,
                twistRight: false,
                twistLeft: false,
                lookLeft: false,
                lookRight: false,
            };

            const getFlagForKeyCode = (keyCode) => {
                switch (keyCode) {
                    case "w":
                        return "moveForward";
                    case "s":
                        return "moveBackward";
                    case "q":
                        return "moveUp";
                    case "e":
                        return "moveDown";
                    case "d":
                        return "moveRight";
                    case "a":
                        return "moveLeft";
                    case "W":
                        return "lookUp";
                    case "S":
                        return "lookDown";
                    case "Q":
                        return "twistRight";
                    case "E":
                        return "twistLeft";
                    case "D":
                        return "lookRight";
                    case "A":
                        return "lookLeft";
                    default:
                        return undefined;
                }
            };

            document.addEventListener(
                "keydown",
                function (e) {
                    const flagName = getFlagForKeyCode(e.key);
                    if (typeof flagName !== "undefined") {
                        flags[flagName] = true;
                    }
                },
                false
            );

            document.addEventListener(
                "keyup",
                function (e) {
                    const flagName = getFlagForKeyCode(e.key);
                    if (typeof flagName !== "undefined") {
                        flags[flagName] = false;
                    }
                },
                false
            );

            viewer.clock.onTick.addEventListener(function () {
                const camera = viewer.camera;
                const cameraHeight = ellipsoid.cartesianToCartographic(
                    camera.position
                ).height;
                const moveRate = cameraHeight / 200.0;
                const rotateRate = 0.01;

                if (flags.moveForward) {
                    camera.moveForward(moveRate);
                }
                if (flags.moveBackward) {
                    camera.moveBackward(moveRate);
                }
                if (flags.moveUp) {
                    camera.moveUp(moveRate);
                }
                if (flags.moveDown) {
                    camera.moveDown(moveRate);
                }
                if (flags.moveLeft) {
                    camera.moveLeft(moveRate);
                }
                if (flags.moveRight) {
                    camera.moveRight(moveRate);
                }
                if (flags.lookRight) {
                    camera.lookRight(rotateRate);
                }
                if (flags.lookLeft) {
                    camera.lookLeft(rotateRate);
                }
                if (flags.twistRight) {
                    camera.twistRight(rotateRate);
                }
                if (flags.twistLeft) {
                    camera.twistLeft(rotateRate);
                }
                if (flags.lookUp) {
                    camera.lookUp(rotateRate);
                }
                if (flags.lookDown) {
                    camera.lookDown(rotateRate);
                }
            });
        };

    }

}

class Listen {
    constructor(viewer) {

        this.listenPosition = (
            position,
            callback
        ) => {
            const cartesian3 = Cesium.Cartesian3.fromDegrees(position[0], position[1]);
            const listen = viewer.scene.postRender.addEventListener(() =>
                callback(cartesian3)
            );
            return listen;
        };

        this.removeListener = (listener) => {
            listener && listener();
        };

        this.listenModelEntity = (modelEntity) => {
            if (viewer.clock.shouldAnimate === true) {
                const ori = modelEntity.orientation.getValue(
                    viewer.clock.currentTime
                );
                const center = modelEntity.position.getValue(
                    viewer.clock.currentTime
                );
                let transform = Cesium.Transforms.eastNorthUpToFixedFrame(center);
                transform = Cesium.Matrix4.fromRotationTranslation(
                    Cesium.Matrix3.fromQuaternion(ori),
                    center
                );
                viewer.camera.lookAtTransform(
                    transform,
                    new Cesium.Cartesian3(-2, 0, 0)
                );
            }
        };

    }
}

class SceneMode {
    constructor(viewer) {
        this.camera = new Camera(viewer);
        this.Scene2D = () => {
            viewer.scene.morphTo2D(0.2);
            setTimeout(() => {
                this.camera.flyTo({
                    lon: position0[0],
                    lat: position0[1],
                    height: position0[2],
                    duration: 0.5,
                });
            }, 600);
        };
        this.Scene3D = () => {
            viewer.scene.morphTo3D(0.2);
            setTimeout(() => {
                this.camera.flyTo({
                    lon: position0[0],
                    lat: position0[1],
                    height: position0[2],
                    duration: 0.6,
                });
            }, 800);
        };
        this.ColumbusView = () => {
            viewer.scene.morphToColumbusView(0.2);
            setTimeout(() => {
                this.camera.flyTo({
                    lon: position0[0],
                    lat: position0[1],
                    height: position0[2],
                    duration: 0.6,
                });
            }, 800);
        };

    }


}

class Time {
    constructor(viewer) {

        this.createTimeline = ({
                                   duration = 4, // 4 days
                                   clockRange = Cesium.ClockRange.UNBOUNDED, // always
                                   multiplier = 1, // speed = 1
                               }) => {
            const timeStart = Cesium.JulianDate.fromDate(new Date());
            const timeEnd = Cesium.JulianDate.addDays(
                timeStart,
                duration,
                new Cesium.JulianDate()
            );
            viewer.clock.startTime = timeStart.clone();
            viewer.clock.stopTime = timeEnd.clone();
            viewer.clock.currentTime = timeStart.clone();
            viewer.clock.clockRange = clockRange; // UNBOUNDED(:always), CLAMPED(:stop), LOOP_STOP(:loop)
            viewer.clock.multiplier = multiplier; // time speed
            viewer.clock.shouldAnimate = true;
        };

        this.activateTimeline = () => {
            viewer.clock.shouldAnimate = true;
        };

        this.inactivateTimeline = () => {
            viewer.clock.shouldAnimate = false;
        };

        this.pause = () => {
            viewer.clock.shouldAnimate = !viewer.clock.shouldAnimate;
            // if (viewer.clock.shouldAnimate) {
            //     viewer.scene.preUpdate.addEventListener(listenerFirstPerspective);
            // } else {
            //     viewer.scene.preUpdate.removeEventListener(listenerFirstPerspective);
            //     viewer.camera.lookAtTransform(Cesium.Matrix4.IDENTITY); // switch mouse control model
            // }
        };

    }
}

class Roam {
    constructor(viewer) {
        this.modelEntity = undefined;

        this.cameraLookAtEntity = (entity) => {
            if (viewer.clock.shouldAnimate === true) {
                const ori = entity.orientation.getValue(
                    viewer.clock.currentTime
                );
                const center = entity.position.getValue(
                    viewer.clock.currentTime
                );
                let transform = Cesium.Transforms.eastNorthUpToFixedFrame(center);
                transform = Cesium.Matrix4.fromRotationTranslation(
                    Cesium.Matrix3.fromQuaternion(ori),
                    center
                );
                viewer.camera.lookAtTransform(
                    transform,
                    new Cesium.Cartesian3(-2, 0, 0)
                );
            }
        };

        this.Roaming = ({velocity = 15}) => {
            viewer.entities && viewer.entities.removeAll();

            const coordinates = [
                [119.78628475809859, 25.50347264740604, 5.613364833908882],
                [119.78650124187148, 25.503752812993547, 4.7107064190196315],
                [119.78654547138368, 25.503812188050603, 4.993588853426436],
            ];

            const c3Array = coordinates.map((coordinate) => {
                return Cesium.Cartesian3.fromDegrees(
                    coordinate[0],
                    coordinate[1],
                    coordinate[2]
                );
            });

            this.modelEntity = viewer.entities.add({
                position: c3Array[0],
            });

            const property = new Cesium.SampledPositionProperty();

            const start1 = Cesium.JulianDate.fromDate(new Date());
            let totalDistance = 0;
            let totalTime;
            const velocityKmh = velocity;
            const velocityMs = (velocityKmh * 1000) / 3600;

            for (let i = 0; i < c3Array.length; i++) {
                if (i !== 0) {
                    const deltDistance = Cesium.Cartesian3.distance(
                        c3Array[i - 1],
                        c3Array[i]
                    );
                    totalDistance += deltDistance;
                }
                totalTime = totalDistance / velocityMs;

                const time = Cesium.JulianDate.addSeconds(
                    start1,
                    totalTime,
                    new Cesium.JulianDate()
                );

                property.addSample(time, c3Array[i]);
            }

            const loopProperty = new Cesium.CallbackProperty(
                (time) => {
                    const mod = time.secondsOfDay % totalTime;
                    const relativeTime = Cesium.JulianDate.addSeconds(
                        start1,
                        mod,
                        new Cesium.JulianDate()
                    );
                    return property.getValue(relativeTime);
                },
                false
            );
            // @ts-ignore
            this.modelEntity.position = loopProperty;
            this.modelEntity.orientation = new Cesium.VelocityOrientationProperty(
                // @ts-ignore
                loopProperty
            );

            viewer.scene.preUpdate.addEventListener(() =>
                this.cameraLookAtEntity(this.modelEntity)
            );
        };
    }
}

export class Tiles {
    constructor(viewer) {
        this.dbIdToFeatures = {};
        this.BIM = {};
        this.tilesetModelMap = {}; // 3DTiles模型缓存
        this.lastHighlightedModel = {
            feature: undefined,
            originalColor: undefined,
        }; // 上一个高亮的模型
        this.hightModelTime = null; // 高亮模型定时器

        const update3dtilesMaxtrix = ({
                                          type,
                                          scale = 1.0,
                                          longitude = 115.57,
                                          latitude = 36.26,
                                          height = 800,
                                          rx = 0,
                                          ry = 0,
                                          rz = 0,
                                          alpha = 1,
                                      }) => {
            const mx = Cesium.Matrix3.fromRotationX(Cesium.Math.toRadians(rx));
            const my = Cesium.Matrix3.fromRotationY(Cesium.Math.toRadians(ry));
            const mz = Cesium.Matrix3.fromRotationZ(Cesium.Math.toRadians(rz));
            const rotationX = Cesium.Matrix4.fromRotationTranslation(mx);
            const rotationY = Cesium.Matrix4.fromRotationTranslation(my);
            const rotationZ = Cesium.Matrix4.fromRotationTranslation(mz);
            const position = Cesium.Cartesian3.fromDegrees(longitude, latitude, height);
            const m = Cesium.Transforms.eastNorthUpToFixedFrame(position);

            Cesium.Matrix4.multiply(m, rotationX, m);
            Cesium.Matrix4.multiply(m, rotationY, m);
            Cesium.Matrix4.multiply(m, rotationZ, m);
            const scaleMatrix = Cesium.Matrix4.fromUniformScale(scale);
            Cesium.Matrix4.multiply(m, scaleMatrix, m);

            this.BIM[type]._root.transform = m;
        }

        this.load3Dtiles = ({
                                type,
                                url,
                                position = [117, 31],
                                height = 0,
                                rotation = [0, 0, 0],
                                scale = 1,
                                alpha = 1,
                            }) => {
            const tileset = new Cesium.Cesium3DTileset({url});
            tileset.enableModelExperimental = false;
            tileset.show = true;
            viewer.scene.primitives.add(tileset);
            this.BIM[type] = tileset;
            update3dtilesMaxtrix({
                type,
                scale,
                longitude: position[0],
                latitude: position[1],
                height,
                rx: rotation[0],
                ry: rotation[1],
                rz: rotation[2],
                alpha,
            });
        }

        this.load3DtilesPark = ({
                                    type,
                                    url,
                                    glsl,
                                    show = true,
                                }) => {
            if (this.BIM[type] === undefined) {
                const tileset = new Cesium.Cesium3DTileset({url});
                tileset.enableModelExperimental = false;
                viewer.scene.primitives.add(tileset);
                this.BIM[type] = tileset;
            } else {
                this.BIM[type].show = true;
            }
        }

        this.load3DtilesPipe = ({
                                    type,
                                    url,
                                    show = true,
                                }) => {
            if (this.BIM[type] === undefined) {
                this.BIM[type] = viewer.scene.primitives.add(
                    // @ts-ignore
                    new Cesium.Cesium3DTileset({url})
                );

                this.BIM[type].readyPromise.then((tileset) => {
                    attachTileset(tileset);
                });
                this.BIM[type].show = show;
            }
        }

        this.load3DtilesCity = ({
                                    type,
                                    url,
                                    glsl,
                                    show = true,
                                }) => {
            // @ts-ignore
            const tileset = new Cesium.Cesium3DTileset({url});
            tileset.style = new Cesium.Cesium3DTileStyle({
                color: {
                    conditions: [["true", "color('rgb(51, 153, 255)',1)"]],
                },
            });
            tileset.show = show;
            viewer.scene.primitives.add(tileset);
            this.BIM[type] = tileset;
        }

        const attachTileset = (tileset) => {
            const getFeatureDbId = (feature) => {
                if (Cesium.defined(feature) && Cesium.defined(feature.getProperty)) {
                    return parseInt(feature.getProperty("DbId"), 10);
                }
                return -1;
            };

            const loadFeature = (feature) => {
                const dbId = getFeatureDbId(feature);
                let features = this.dbIdToFeatures[dbId];
                if (!Cesium.defined(features)) {
                    this.dbIdToFeatures[dbId] = features = [];
                }
                features.push(feature);
            };

            const processContentFeatures = (
                content,
                callback
            ) => {
                const featuresLength = content.featuresLength;
                for (let i = 0; i < featuresLength; ++i) {
                    const feature = content.getFeature(i);
                    callback(feature);
                }
            };

            const processTileFeatures = (
                tile,
                callback
            ) => {
                const content = tile.content;
                const innerContents = content.innerContents;
                if (Cesium.defined(innerContents)) {
                    const length = innerContents.length;
                    for (let i = 0; i < length; ++i) {
                        processContentFeatures(innerContents[i], callback);
                    }
                } else {
                    processContentFeatures(content, callback);
                }
            };

            tileset.tileLoad.addEventListener((tile) => {
                processTileFeatures(tile, loadFeature);
            });
        }

        this.show3Dtiles = (type, show = true) => {
            if (this.BIM[type] !== undefined) {
                this.BIM[type].show = show;
            }
        }

        this.destroy = (type) => {
            if (this.BIM[type] !== undefined) {
                this.BIM[type].destroy();
                // @ts-ignore
                this.BIM[type] = undefined;
            }
        }

        // 控制3D Tiles模型显隐
        this.toggleTilesetVisibleById = (layerId, show = true) => {
            const tileset = this.tilesetModelMap[layerId];
            if (tileset) {
                tileset.show = show;
            }
        };

        // 动态更新3D Tiles模型的变换矩阵
        this.update3DTilesModelMatrix = (
            layerId,
            { position, rotation = [0, 0, 0], scale = 1 }
        ) => {
            const tileset = this.tilesetModelMap[layerId];
            if (!tileset) return;
            const [lon, lat, height] = position;
            const [rx, ry, rz] = rotation;
            const mx = Cesium.Matrix3.fromRotationX(Cesium.Math.toRadians(rx));
            const my = Cesium.Matrix3.fromRotationY(Cesium.Math.toRadians(ry));
            const mz = Cesium.Matrix3.fromRotationZ(Cesium.Math.toRadians(rz));
            const rotationX = Cesium.Matrix4.fromRotationTranslation(mx);
            const rotationY = Cesium.Matrix4.fromRotationTranslation(my);
            const rotationZ = Cesium.Matrix4.fromRotationTranslation(mz);
            const pos = Cesium.Cartesian3.fromDegrees(lon, lat, height);
            let m = Cesium.Transforms.eastNorthUpToFixedFrame(pos);
            Cesium.Matrix4.multiply(m, rotationX, m);
            Cesium.Matrix4.multiply(m, rotationY, m);
            Cesium.Matrix4.multiply(m, rotationZ, m);
            const scaleMatrix = Cesium.Matrix4.fromUniformScale(scale);
            Cesium.Matrix4.multiply(m, scaleMatrix, m);
            tileset.modelMatrix = m;
        };

        // 切换地球半透明效果
        this.toggleTranslucency = (show) => {
            if (show) {
                viewer.scene.globe.translucency.enabled = true;
                viewer.scene.globe.translucency.frontFaceAlpha = 0;
                viewer.scene.globe.translucency.backFaceAlpha = 0;
            } else {
                viewer.scene.globe.translucency.enabled = false;
                viewer.scene.globe.translucency.frontFaceAlpha = 1.0;
                viewer.scene.globe.translucency.backFaceAlpha = 1.0;
            }
        };

        // 高亮模型
        this.highlightedModel = (pickingEntity) => {
            if (this.hightModelTime) {
                clearInterval(this.hightModelTime);
            }

            // 恢复上一个高亮要素的显示和颜色
            if (pickingEntity !== this.lastHighlightedModel.feature) {
                if (this.lastHighlightedModel.feature != undefined) {
                    this.lastHighlightedModel.feature.color =
                        this.lastHighlightedModel.originalColor;
                    this.lastHighlightedModel.feature = pickingEntity;
                    this.lastHighlightedModel.originalColor = pickingEntity.color;
                }
                // 保存当前选中的要素信息
                this.lastHighlightedModel = {
                    feature: pickingEntity,
                    originalColor: pickingEntity.color,
                };
            }
            let timeSun = 0.1;
            this.hightModelTime = setInterval(function () {
                timeSun = timeSun >= 1.0 ? 0.1 : timeSun + 0.1;
                pickingEntity.color = Cesium.Color.ORANGE.withAlpha(timeSun);
            }, 100);
        };

        this.clearHighlightedModel = () => {
            if (this.hightModelTime) {
                clearInterval(this.hightModelTime);
            }
            if (this.lastHighlightedModel.feature != undefined) {
                //还原前选择要素的本颜色
                this.lastHighlightedModel.feature.color =
                    this.lastHighlightedModel.originalColor;
            }
            this.lastHighlightedModel = {
                feature: undefined,
                color: undefined,
            };
        };

    }
}

export class PolylineFlowMaterialProperty extends Cesium.PolylineOutlineMaterialProperty {
    constructor({options = {}, type, image, time, imageW, source, imageColor}) {
        super(options)
        this.createMaterial({type, image, time, imageW, source, imageColor})
        this.type = type
    }

    createMaterial({type, image, time, imageW = 15, source, imageColor = new Cesium.Cartesian3(1.0, 1.0, 1.0)}) {
        Cesium.Material[type] = type
        Cesium.Material._materialCache.addMaterial(type, {
            // strict: true,
            fabric: {
                type,
                uniforms: {
                    image,
                    time,
                    imageW, //贴图比例，每种线条的贴图宽度不同，贴图拉伸不同，需要给出参数
                    imageColor
                },
                source
            }
            // translucent: true
        })
    }

    getType() {
        return this.type
    }
}
