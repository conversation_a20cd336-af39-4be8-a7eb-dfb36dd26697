<template>
    <div class="open-space-diffusion-model">
        <div class="analysis-header">
            <div class="title">开放空间扩散范围分析</div>
            <div class="close-btn" @click="$emit('close')">
                <el-icon>
                    <Close/>
                </el-icon>
            </div>
        </div>

        <div class="content-area">
            <div class="form-container">
                <div class="form-row">
                    <div class="form-group">
                        <label class="form-label">日照：</label>
                        <el-select v-model="formData.sunlight" placeholder="请选择日照条件" class="form-select">
                            <el-option
                                v-for="option in sunlightOptions"
                                :key="option.value"
                                :label="option.label"
                                :value="option.value"
                            />
                        </el-select>
                    </div>
                    <div class="form-group">
                        <label class="form-label">环境压力(Pa)：</label>
                        <el-input v-model="formData.environmentalPressure"
                                  placeholder="请输入环境压力"
                                  class="form-input"
                                  size="small" />
                    </div>
                </div>

                <div class="form-row">
                    <div class="form-group">
                        <label class="form-label">风速：</label>
                        <el-select v-model="formData.windSpeed" placeholder="请选择风速" class="form-select">
                            <el-option
                                v-for="option in windSpeedOptions"
                                :key="option.value"
                                :label="option.label"
                                :value="option.value"
                            />
                        </el-select>
                    </div>
                    <div class="form-group">
                        <label class="form-label">管道压力(MPa)：</label>
                        <el-input v-model="formData.pipelinePressure"
                                  placeholder="请输入管道压力"
                                  class="form-input"
                                  size="small" />
                    </div>
                </div>

                <div class="form-row">
                    <div class="form-group">
                        <label class="form-label">风向：</label>
                        <el-select v-model="formData.windDirection" placeholder="请选择风向" class="form-select">
                            <el-option
                                v-for="option in windDirectionOptions"
                                :key="option.value"
                                :label="option.label"
                                :value="option.value"
                            />
                        </el-select>
                    </div>
                    <div class="form-group">
                        <label class="form-label">泄露孔口面积(cm²)：</label>
                        <el-input v-model="formData.leakArea"
                                  placeholder="请输入泄露孔口面积"
                                  class="form-input"
                                  size="small"/>
                    </div>
                </div>

                <div class="form-row">
                    <div class="form-group">
                        <label class="form-label">泄露孔口形状：</label>
                        <el-select v-model="formData.leakShape" placeholder="请选择泄露孔口形状" class="form-select">
                            <el-option
                                v-for="option in leakShapeOptions"
                                :key="option.value"
                                :label="option.label"
                                :value="option.value"
                            />
                        </el-select>
                    </div>
                    <div class="form-group">
                        <!-- 占位符，保持布局对称 -->
                    </div>
                </div>

                <div class="form-actions">
                    <el-button type="primary" class="analyze-btn" @click="handleAnalyze">
                        开始分析
                    </el-button>
                </div>
            </div>
        </div>
    </div>
</template>

<script setup>
import { reactive } from 'vue'
import { Close } from '@element-plus/icons-vue'

const emit = defineEmits(['close', 'analyze'])

// 选项数据
const sunlightOptions = [
    { label: '白天日照-强', value: 'daylight-strong' },
    { label: '白天日照-中', value: 'daylight-medium' },
    { label: '白天日照-弱', value: 'daylight-weak' },
    { label: '夜间日照-多云', value: 'night-many-clouds' },
    { label: '夜间日照-少云', value: 'night-few-clouds' }
];

const windSpeedOptions = [
    { label: '无风', value: 'no-wind' },
    { label: '软风', value: 'soft-wind' },
    { label: '轻风', value: 'light-wind' },
    { label: '微风', value: 'breeze' },
    { label: '和风', value: 'wind' },
    { label: '清劲风', value: 'strong-breeze' },
    { label: '强风', value: 'strong-wind' },
    { label: '疾风', value: 'gale' },
    { label: '大风', value: 'storm' },
    { label: '烈风', value: 'violent-storm' },
    { label: '狂风', value: 'hurricane' },
    { label: '暴风', value: 'medium-wind' },
    { label: '台风', value: 'typhoon' },
];

const windDirectionOptions = [
    { label: '东风', value: 'east' },
    { label: '南风', value: 'south' },
    { label: '西风', value: 'west' },
    { label: '北风', value: 'north' },
    { label: '东北风', value: 'northeast' },
    { label: '东南风', value: 'southeast' },
    { label: '西北风', value: 'northwest' },
    { label: '西南风', value: 'southwest' },
];

const leakShapeOptions = [
    { label: '圆形孔', value: 'circular' },
    { label: '三角形孔', value: 'triangular' },
    { label: '方形孔', value: 'square' },
];

const formData = reactive({
    sunlight: 'daylight-strong',
    windSpeed: 'no-wind',
    windDirection: 'east',
    leakShape: 'circular',
    environmentalPressure: '101325',
    pipelinePressure: '1.6',
    leakArea: '20'
});

const handleAnalyze = () => {
    // 将表单数据传递给父组件
    emit('analyze', formData)
};
</script>

<style lang="scss" scoped>
.open-space-diffusion-model {
  position: fixed;
  top: 50%;
  left: 56.3%;
  transform: translate(-50%, -50%);
  width: 700px;
  display: flex;
  flex-direction: column;
  border-radius: 8px;
  pointer-events: all;

  &::before {
    content: "";
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    backdrop-filter: blur(10px);
    background: rgba(255, 255, 255, 1);
    border-radius: 8px;
    border: 1px solid rgba(255, 255, 255, 0.4);
    z-index: -1;
  }

  .analysis-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    border-top-left-radius: 12px;
    padding: 10px 20px;
    font-size: 16px;
    font-weight: bold;
    color: rgba(50, 50, 50, 0.8);
    border-bottom: 1px solid rgba(200, 200, 200, 0.4);

    .title {
      font-size: 16px;
      font-weight: bold;
      color: rgba(50, 50, 50, 0.8);
    }

    .close-btn {
      font-size: 22px;
      cursor: pointer;
      color: rgba(50, 50, 50, 0.8);

      &:hover {
        opacity: 0.8;
      }
    }
  }

  .content-area {
    flex: 1;
    padding: 10px 20px;
    overflow: auto;

    .form-container {
      .form-row {
        display: flex;
        gap: 10px;
        margin-bottom: 15px;

          .form-group {
           flex: 1;
           display: flex;
           flex-direction: row;
           align-items: center;
           gap: 10px;

           .form-label {
             color: rgba(50, 50, 50, 0.8);
             font-size: 12px;
             font-weight: 500;
             width: 120px;
             text-align: right;
             white-space: nowrap;
           }

           .form-select {
              width: 150px;
              height: 32px;
              :deep(.el-input__wrapper) {
                background: rgba(200, 200, 200, 0.5);
                border: 1px solid rgba(200, 200, 200, 0.2);
                border-radius: 4px;
                box-shadow: none;

                &:hover {
                  border-color: rgba(255, 255, 255, 0.4);
                }

                &.is-focus {
                  border-color: #409eff;
                  box-shadow: 0 0 0 1px rgba(64, 158, 255, 0.2);
                }
              }

              :deep(.el-input__inner) {
                color: rgba(50, 50, 50, 0.8);
                font-size: 12px;

                &::placeholder {
                  color: rgba(50, 50, 50, 0.4);
                }
              }

              :deep(.el-select__caret) {
                color: rgba(50, 50, 50, 0.8);
              }
            }

            .form-input {
              width: 150px;
              height: 32px;
              :deep(.el-input__wrapper) {
                background: rgba(200, 200, 200, 0);
                border: 1px solid rgba(200, 200, 200, 0.5);
                border-radius: 4px;
                box-shadow: none;

                &:hover {
                  border-color: rgba(255, 255, 255, 0);
                }

                &.is-focus {
                  border-color: #409eff;
                  box-shadow: 0 0 0 1px rgba(64, 158, 255, 0.2);
                }
              }

              :deep(.el-input__inner) {
                color: rgba(50, 50, 50, 0.8);
                font-size: 12px;

                &::placeholder {
                  color: rgba(50, 50, 50, 0.4);
                }
              }
            }
        }
      }

      .form-actions {
        display: flex;
        justify-content: center;
        margin-top: 20px;

        .analyze-btn {
          background: linear-gradient(135deg, #409eff 0%, #1890ff 100%);
          border: none;
          border-radius: 6px;
          padding: 16px 24px;
          font-size: 14px;
          font-weight: 500;
          color: #ffffff;
          cursor: pointer;
          transition: all 0.3s ease;

          &:hover {
            background: linear-gradient(135deg, #66b1ff 0%, #40a9ff 100%);
            transform: translateY(-1px);
            box-shadow: 0 4px 12px rgba(64, 158, 255, 0.3);
          }

          &:active {
            transform: translateY(0);
          }
        }
      }
    }
  }
}

:deep(.el-select-dropdown) {
  background: rgba(11, 30, 65, 0.95);
  border: 1px solid #19385c;
  backdrop-filter: blur(10px);

  .el-select-dropdown__item {
    color: rgba(255, 255, 255, 0.9);
    font-size: 12px;

    &:hover {
      background: rgba(255, 255, 255, 0.1);
    }

    &.selected {
      background: #409eff;
      color: #ffffff;
    }
  }
}
</style>